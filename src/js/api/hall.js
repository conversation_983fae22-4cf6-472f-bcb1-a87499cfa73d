$(function() {
  getCaseList(); // 获取用户案例

  handleCase(); // 处理案例
  handleCaseLink(); // 处理案例链接
})
function getCaseList() {
  $.ajax({
    url: 'https://static.huicanzhan.cn/common/zgCase.json',
    type: 'get',
    success: function (res) {
      var $ul = $('.hz_list');
      $.each(res, function(index, item) {
        var tags = item.tag.replace(/\s/g, "").split('#')
        var tagHtml = ''
        tags.forEach(tag=> {
          if (tag != '') {
            tagHtml += `<dd>#${ tag }</dd>` 
          }
        });
        var $li = `
          <li>
            <a class="nei">
              <p class="pimg"><img src="${ item.cover }?x-oss-process=image/resize,w_270,h_180,m_lfit"/></p>
              <div class="txt">
                <h3 class="title">${ item.name}</h3>
                <div class="desc">${ item.desc }</div>
                <dl class="type">${tagHtml}</dl>
                <p class="more more-qrcode" style="display:${item.isChoice ? 'none' : 'block'}">了解更多>></p>
                <p class="more more-link"  data-url="${item.url}" style="display:${item.isChoice ? 'block' : 'none'}">了解更多>></p>
              </div>
              <div class="case-qrcode">
                <div class="tt">微信扫一扫查看</div>
                <img class="qrcode" src="${ item.qrcode }"/>
              </div>
            </a>
          </li>
        `
        $ul.append($li)
      })
    }
  });
}
function handleCase() {
  $(".al_list  .more-qrcode").hover(function(){
    var i=$(".al_list  .more-qrcode").index(this)
    $(".al_list  .case-qrcode").eq(i).show();

  })
  $(document).on({
    mouseenter: function() {
      var i=$(".al_list  .more-qrcode").index(this)
      $(".al_list  .case-qrcode").eq(i).show();
    },
    mouseleave: function() {
      $('.case-qrcode').hide();
    }
  }, '.al_list .more-qrcode');
}
function handleCaseLink() {
  $('.hz_list').on('click','.more-link', function (e) {
    const url = $(this).context.dataset.url
    window.open(url) 
  })
}