$(function() {
  getBoothUrl(); // 获取展位链接

  var total = 0;
  var params = {
    page: 1,
    limit: 20,
    start_at: '',
    finish_at: '',
    city: '',
    date: '', // 日期
  }

  $('.search_date dd').click(function () {
    $(this).addClass('sel').siblings().removeClass('sel');
    params.date = $(this).text();
    params.page = 1;
    params.city = $('.search_city .sel').eq(0).text();
    getEvents(params);
  })
  $('.search_city dd').click(function () {
    $(this).addClass('sel').siblings().removeClass('sel')
    params.date = $('.search_date .sel').eq(0).text();
    params.page = 1;
    params.city = $(this).text();
    getEvents(params);
  })
  
  getEvents(params);
  handleEvent();
  
})

// 活动列表
function getEvents(search) {
  if (search.city === '全国') {
    search.city = '';
  }
  if (search.date == '全部') {
    search.start_at = '';
    search.finish_at = '';
  } else {
    search.start_at = caculateDate(search.date).start_at;
    search.finish_at = caculateDate(search.date).finish_at;
  }
  $.ajax({
    url: 'https://official-api.huicanzhan.cn/events',
    type: 'get',
    data: { 
      page: search.page,
      limit: search.limit,
      start_at: search.start_at,
      finish_at: search.finish_at,
      city: search.city,
     },
    success: function (res) {
      total = res.total; // 总条数
      $('#total').text(`共 ${res.total} 项`)
      $('#page').pagination({
        dataSource: `https://official-api.huicanzhan.cn/events?start_at=${search.start_at}&finish_at=${search.finish_at}&city=${search.city}`,
        locator: 'items',
        totalNumber: total,
        pageSize: 20,
        alias: {
          pageNumber: 'page',
          pageSize: 'limit'
        },
        formatNavigator: '<span class="navigator">共 <%= totalNumber %> 项</span>',
        showNavigator: true,
        activeClassName: 'page-active',
        callback: function (data, pagination) {
          var $ul = $('.ss_list')
          $ul.empty()
          $.each(data, function (index, item) {
            const $li = `
              <li>
                <a href="javascript:;" class="nei" data-id="${item.event_id}">
                  <p class="pimg">
                    <img src="${item.cover}?x-oss-process=image/resize,w_270,h_180,m_lfit">
                  </p>
                  <dl class="txt">
                    <dd class="date">${moment(item.start_at * 1000).format('yyyy-MM-DD')} ~ ${moment(item.finish_at * 1000).format('yyyy-MM-DD')}</dd>
                    <dd class="title">${item.subject}</dd>
                    <dd class="wei">${item.province} ${item.city} ${item.address}</dd>
                  </dl>
                </a>
              </li>
            `
            $ul.append($li)
          })
          $('html,body').animate({ scrollTop: 0 }, 100);
          // console.log(data, pagination)
        }
      })
    }
  })
}

function handleEvent() {
  $('.ss_list').on('click','.nei', function (e) {
    const id = $(this).context.dataset.id
    window.location.href = `./event.html?eventId=${id}`
  })
}
// 计算时间
function caculateDate(date) {
  const formatDate = {
    start_at: '',
    finish_at: '',
  };
  const nowDate = moment().format('yyyy-MM-DD');
  switch (date) {
    case '今天':
      formatDate.start_at = nowDate;
      formatDate.finish_at = moment().format('yyyy-MM-DD');
      return formatDate;
    case '明天':
      formatDate.start_at = moment().add(1, 'days').format('yyyy-MM-DD');
      formatDate.finish_at = moment().add(1, 'days').format('yyyy-MM-DD');
      return formatDate;
    case '最近一周':
      formatDate.start_at = nowDate;
      formatDate.finish_at = moment().add(7, 'days').format('yyyy-MM-DD');
      return formatDate;
    case '最近一个月':
      formatDate.start_at = nowDate;
      formatDate.finish_at = moment().add(30, 'days').format('yyyy-MM-DD');
      return formatDate;
    default:
      return formatDate;
  }
}