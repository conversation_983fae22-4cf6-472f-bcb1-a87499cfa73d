$(function() {
  const a_dl = $('.a_dl');
  const a_fb = $('.a_fb');
  const a_dl_m = $('.a_dl_m');
  const a_fb_m = $('.a_fb_m');
  a_dl.click(function () {
    toLogin()
  })
  a_fb.click(function () {
    toRegister()
  })
  a_dl_m.click(function () {
    toLogin()
  })
  a_fb_m.click(function () {
    toRegister()
  })
})

function toLogin() {
  const targetUrl = 'https://huicanzhan.cn/cdmp/login';
  const newUrl = mergeUrlParams(targetUrl, window.location.href, { channel: 'website' });
  window.location.href = newUrl;
}
function toRegister() {
  const targetUrl = 'https://huicanzhan.cn/cdmp/register';
  const newUrl = mergeUrlParams(targetUrl, window.location.href, { channel: 'website' });
  window.location.href = newUrl;
}
/**
 * 将源URL的查询参数拼接到目标URL上，并添加额外参数
 * @param {string} targetUrl - 目标URL（要拼接到的URL）
 * @param {string} sourceUrl - 源URL（提供查询参数的URL）
 * @param {Object} extraParams - 要额外添加的参数对象 {key: value}
 * @returns {string} 处理后的URL
 */
function mergeUrlParams(targetUrl, sourceUrl, extraParams = {}) {
  // 处理目标URL
  const [targetBase, targetHash] = targetUrl.split('#');
  const targetUrlObj = new URL(
    targetBase.includes('://') 
      ? targetBase 
      : `https://huicanzhan.cn${targetBase.startsWith('/') ? '' : '/'}${targetBase}`
  );

  // 处理源URL的查询参数
  const sourceUrlObj = new URL(
    sourceUrl.includes('://') 
      ? sourceUrl 
      : `https://huicanzhan.cn${sourceUrl.startsWith('/') ? '' : '/'}${sourceUrl}`
  );

  // 合并源URL的查询参数到目标URL
  sourceUrlObj.searchParams.forEach((value, key) => {
    targetUrlObj.searchParams.set(key, value);
  });

  // 添加额外参数
  Object.entries(extraParams).forEach(([key, value]) => {
    targetUrlObj.searchParams.set(key, value);
  });

  // 重新构建URL
  let result = targetBase.includes('://')
    ? `${targetUrlObj.origin}${targetUrlObj.pathname}${targetUrlObj.search}`
    : `${targetUrlObj.pathname}${targetUrlObj.search}`.replace(/^\/+/, '');

  // 添加回hash
  if (targetHash) {
    result += `#${targetHash}`;
  }

  return result;
}




// 获取动态展位图
function getBoothUrl() {
  $.ajax({
    url: 'https://official-api.huicanzhan.cn/booth-layout',
    type: 'get',
    success: function (res) {
      $('.boothUrl').attr('href', res.url);
    }
  })
}

//获取url中的参数
function getUrlParam(name) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
  var r = window.location.search.substr(1).match(reg); //匹配目标参数
  if (r != null) return unescape(r[2]); return null; //返回参数值
}

let provinceOpt = [];

function initApplyForm() {
  getCity();
  getScale();
  getDemand();
  getIndustry();
  handleProvince();
  handleSubmit();
}

// 获取省市列表
function getCity() {
  $.ajax({
    url: 'https://static.huicanzhan.cn/common/city.json',
    type: 'get',
    success: function (res) {
      provinceOpt = res;
      var $province = $('.province');
      $.each(res, function (index, item) {
        $province.append(`<option value="${item.name}">${item.name}</option>`)
      })
      
    }
  })
}

function handleProvince() {
  var province = $('.province');
  province.change(function(){
    var $city = $('.city');
    $city.empty(); // 清空
    $city.append(`<option value="">请选择市</option>`)
    var proVal = province.val();
    if (!proVal) {
      return;
    }
    var cityOpt = provinceOpt.filter(item => item.name == proVal)[0].items;
    $.each(cityOpt, function (index, item) {
      $city.append(`<option value="${item.name}">${item.name}</option>`)
    })
  });
  
}

// 获取活动规模
function getScale() {
  $.ajax({
    url: 'https://official-api.huicanzhan.cn/scale-default',
    type: 'get',
    success: function (res) {
      var $scale = $('.scale');
      $.each(res.items, function (index, item) {
        $scale.append(`<label><input type="radio" name="rad1" value="${item}">${item}</label>`)
      })
    }
  })
}

// 获取活动需求
function getDemand() {
  $.ajax({
    url: 'https://official-api.huicanzhan.cn/demand-default',
    type: 'get',
    success: function (res) {
      var $need = $('.need');
      $.each(res.items, function (index, item) {
        $need.append(`<label><input type="checkbox" name="rad1" value="${item}">${item}</label>`)
      })
    }
  })
}

// 获取所属行业
function getIndustry() {
  $.ajax({
    url: 'https://official-api.huicanzhan.cn/industry-default',
    type: 'get',
    success: function (res) {
      var $industry = $('.industry');
      $.each(res.items, function (index, item) {
        $industry.append(`<label><input type="checkbox" name="rad1" value="${item}">${item}</label>`)
      })
    }
  })
}

function handleSubmit() {
  $('.tan_shiyong .btn2').click(function () {
    var name = $('.name').val();
    var mobile = $('.mobile').val();
    var company = $('.company').val();
    var province = $('.province').val();
    var city = $('.city').val();
    var scale = $('.scale input:checked').val();
    var need = [];
    var industry = [];
    $('.need input:checked').each(function () {
      need.push($(this).val());
    })
    $('.industry input:checked').each(function () {
      industry.push($(this).val());
    })
    var remark = $('.remark').val();

    if (!name) {
      return alert('请输入姓名');
    }
    if (!mobile) {
      return alert('请输入手机号');
    }
    if (!company) {
      return alert('请输入公司/单位');
    }
    if (!province) {
      return alert('请选择省份');
    }
    if (!city) {
      return alert('请选择城市');
    }
    if (!scale) {
      return alert('请选择活动规模');
    }
    if (!need.length) {
      return alert('请选择活动需求');
    }
    if (!industry.length) {
      return alert('请选择所属行业');
    }
    $.ajax({
      url: 'https://official-api.huicanzhan.cn/applies',
      type: 'post',
      data: {
        name: name,
        mobile: mobile,
        company: company,
        province: province,
        city: city,
        scale: scale,
        demand: need,
        industries: industry,
        remark: remark
      },
      success: function (res) {
        alert('提交成功');
        handleReset();
        layer.closeAll(); // 关闭所有弹窗
        $("body").removeClass("body_hidden");
      }
    })
  })
}

function handleReset() {
  $('.name').val('');
  $('.mobile').val('')
  $('.company').val('');
  $('.province').val('');
  $('.city').val('');
  $('.scale input').prop('checked', false);
  $('.need input').prop('checked', false);
  $('.industry input').prop('checked', false);
  $('.remark').val('');
}