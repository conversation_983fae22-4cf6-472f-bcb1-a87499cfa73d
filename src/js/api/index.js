$(function () {
  getBoothUrl();
  getStat();
  getPopulars();
  getCases();
  getPartners();
  handlePopular();
})

// 统计数据
function getStat() {
  $.ajax({
    url: 'https://official-api.huicanzhan.cn/stat',
    type: 'get',
    success: function (res) {
      var signin_number = Math.floor(res.signin_number /10000);
      $('.ind_one .stat1').html(res.event_number)
      $('.ind_one .stat2').html(res.sponsor_number)
      $('.ind_one .stat3').html(signin_number)
      //数值跳动
      visualData($('.num-move'));
    }
  })
}      
      
// 热门活动
function getPopulars() {
  $.ajax({
    url: 'https://official-api.huicanzhan.cn/popular-events',
    type: 'get',
    success: function (res) {
      const arr = spArr(res.items, 9)
      $.each(arr, function (idx, temp) {
        var $item = $('<div class="swiper-slide"></div>')
        var $ul = $('<ul class="list"></ul>')
        $.each(temp, function (index, item) {
          const $li = `
            <li>
              <a href="javascript:;" class="nei" data-id="${item.event_id}">
                <p class="pimg"><img src="${item.event.cover}" alt="${item.event.subject}"></p>
                <p class="time">${moment(item.event.start_at * 1000).format('yyyy-MM-DD')} ~ ${moment(item.event.finish_at * 1000).format('yyyy-MM-DD')}</p>
                <h3 class="title">${item.event.subject}</h3>
                <p class="address">${item.event.province} ${item.event.city} ${item.event.address}</p>
              </a>
            </li>
          `
          $ul.append($li)
        })
        $item.append($ul)
        $('.popular-container').append($item)

      })

      new Swiper('.ind_activity_swiper', {
        autoplay: false,
        speed: 500,
        effect: 'slide',
        slidesPerView: 1,
        spaceBetween: 1,
        slidesPerGroup: 1,
        loop: true,
        spaceBetween: 0,
        navigation: {
          nextEl: '.ind_activity .prev',
          prevEl: '.ind_activity .next',
        },
        pagination: {
          el: '.ind_activity .pagination',
          clickable: true,
          type: 'fraction'
        },
      });
    }
  })
}
// 案例
function getCases() {
  $.ajax({
    url: 'https://ocean-api.huicanzhan.cn/official/customer-cases?page=1&limit=200&isChoice=true',
    type: 'get',
    success: function (res) {
      var $ul = $('.ind_case .list')
      const data = res.data.items.slice(0, 3)
      $.each(data, function (index, item) {
        const $li = `
          <li>
            <a href="${item.url}" class="nei">
              <p class="pimg"><img src="${item.cover}" class="img" /></p>
              <div class="txt">
                <h3 class="tit">${item.name}</h3>
                <div class="desc"> ${item.desc} </div>
              </div>
            </a>
          </li>
        `
        $ul.append($li)
      })
    }
  })
}
// 合作伙伴
function getPartners() {
  $.ajax({
    url: 'https://official-api.huicanzhan.cn/partners',
    type: 'get',
    success: function (res) {
      var $ul = $('.ind_three .list')
      $.each(res.items, function (index, item) {
        const $li = `
          <li>
            <a href="${item.link}">
              <img src="${item.url}" alt="${item.sponsor.company}" />
            </a>
          </li>
        `
        $ul.append($li)
      })
    }
  })
}

function handlePopular() {
  $('.popular-container').on('click','.nei', function (e) {
    const id = $(this).context.dataset.id
    window.location.href = `./event.html?eventId=${id}`
  })
}
function spArr(arr, num) {
  let newArr = [];
  for (let i = 0; i < arr.length;) {
    newArr.push(arr.slice(i, (i += num)));
  }
  return newArr;
}