$(function() {
  getBoothUrl(); // 获取展位链接
  getCaseList(); // 获取用户案例

  handleCase(); // 处理案例
})
function getCaseList() {
  $.ajax({
    url: 'https://ocean-api.huicanzhan.cn/official/customer-cases?page=1&limit=200&isChoice=false',
    type: 'get',
    success: function (res) {
      var $ul = $('.hz_list');
      const data = res.data.items.slice(0, 4)
      $.each(data, function(index, item) {
        var tags = item.tag.replace(/\s/g, "").split('#')
        var tagHtml = ''
        tags.forEach(tag=> {
          if (tag != '') {
            tagHtml += `<dd>#${ tag }</dd>` 
          }
        });
        var $li = `
          <li>
            <a class="nei">
              <p class="pimg"><img src="${ item.cover }?x-oss-process=image/resize,w_270,h_180,m_lfit"/></p>
              <div class="txt">
                <h3 class="title">${ item.tag}</h3>
                <div class="desc">${ item.desc }</div>
                <dl class="type">${tagHtml}</dl>
                <p class="more">了解更多>></p>
              </div>
              <div class="case-qrcode">
                <div class="tt">微信扫一扫查看</div>
                <img class="qrcode" src="${ item.qrcode }"/>
              </div>
            </a>
          </li>
        `
        $ul.append($li)
      })
    }
  });
}
function handleCase() {
  $(".al_list  .more").hover(function(){
    var i=$(".al_list  .more").index(this)
    $(".al_list  .case-qrcode").eq(i).show();

  })
  $(document).on({
    mouseenter: function() {
      var i=$(".al_list  .more").index(this)
      $(".al_list  .case-qrcode").eq(i).show();
    },
    mouseleave: function() {
      $('.case-qrcode').hide();
    }
  }, '.al_list .more');
}