$(function () {
  getBoothUrl(); // 获取展位链接

  let service_id = '' 
  service_id = getUrlParam('id');
  getServiceInfo(service_id);
  getProList(service_id);
})
let tel = ''
// 获取展商详情
function getServiceInfo(service_id) {
  $.ajax({
    url: 'https://ocean-api.huicanzhan.cn/official/servicers/' + service_id,
    type: 'get',
    success: function (res) {
      const company= res.data;
      $('.left_img img').attr('src', company.logo);
      $('.company').text(company.company);
      $('.address').text(company.provinceCity);
      $('.intro').text(company.desc);
      $(".a_zixun").html("咨询电话："+company.mobile)
    }
  })
}
// 获取产品列表
function getProList(service_id) {
  $.ajax({
    url: 'https://ocean-api.huicanzhan.cn/official/servicer-products?servicerId=' + service_id,
    type: 'get',
    success: function (res) {
      var $ul = $('.fw_two .list');
      $ul.empty();
      $.each(res.data.items, function(index,item) {
        var $li = `
          <li>
            <a href="" class="nei">
              <p class="pimg"><img src="${item.image}"></p>
              <div class="bottom">
                <h3 class="title">${item.name}</h3>
                <div class="desc">${item.desc}</div>
              </div>
            </a>
          </li>
        `
        $ul.append($li)
      })
    }
  })
}