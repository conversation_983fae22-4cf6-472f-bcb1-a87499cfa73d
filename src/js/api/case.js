$(function() {
  getBoothUrl(); // 获取展位链接
  getCaseList(); // 获取用户案例
  getJXCaseList(); // 获取精选案例

  handleCase(); // 处理案例
})

// 更多案例
function getCaseList() {
  $.ajax({
    url: 'https://ocean-api.huicanzhan.cn/official/customer-cases?page=1&limit=200&isChoice=false',
    type: 'get',
    success: function (res) {
      var $ul = $('.more_al');
      $.each(res.data.items, function(index, item) {
        var tags = item.tag.replace(/\s/g, "").split('#')
        var tagHtml = ''
        tags.forEach(tag=> {
          if (tag != '') {
            tagHtml += `<dd>#${ tag }</dd>` 
          }
        });
        var $li = `
          <li>
            <a class="nei">
              <p class="pimg"><img src="${ item.cover }?x-oss-process=image/resize,w_270,h_180,m_lfit"/></p>
              <div class="txt">
                <h3 class="title">${ item.tag}</h3>
                <div class="desc">${ item.desc }</div>
                <dl class="type">${tagHtml}</dl>
                <p class="more">了解更多>></p>
              </div>
              <div class="case-qrcode">
                <div class="tt">微信扫一扫查看</div>
                <img class="qrcode" src="${ item.qrcode }"/>
              </div>
            </a>
          </li>
        `
        $ul.append($li)
      })
    }
  });
}
// 精选案例
function getJXCaseList() {
  $.ajax({
    url: 'https://ocean-api.huicanzhan.cn/official/customer-cases?page=1&limit=200&isChoice=true',
    type: 'get',
    success: function (res) {
      //var $ul = $('.al_three');
      var $ul = $('.jingXuan_al');
      $.each(res.data.items, function(index, item) {
        
        var $li = `
          <li>
            <a href="${item.url}" class="nei" target="_blank">
              <img src="${ item.cover }?x-oss-process=image/resize,w_270,h_180,m_lfit" class="img">
              <div class="txt">
                <h3 class="tit">${ item.name }</h3>
                <div class="desc">${ item.desc }</div>
              </div>
            </a>
          </li>
        `
        /*
        var $li = `
          <li>
            <a class="nei">
              <p class="pimg"><img src="${ item.cover }?x-oss-process=image/resize,w_270,h_180,m_lfit"/></p>
              <div class="txt">
                <h3 class="title">${ item.tag}</h3>
                <div class="desc">${ item.desc }</div>
                <dl class="type"><dd>#专业展</dd><dd>#小程序</dd><dd>#数字化服务</dd></dl>
                <p class="more">了解更多>></p>
              </div>
              <div class="case-qrcode">
                <div class="tt">微信扫一扫查看</div>
                <img class="qrcode" src="${ item.qrcode }"/>
              </div>
            </a>
          </li>
        `*/
        $ul.append($li)
      })
    }
  });
}

function handleCase() {
  $(".al_list  .more").hover(function(){
    var i=$(".al_list  .more").index(this)
    $(".al_list  .case-qrcode").eq(i).show();

  })
  $(document).on({
    mouseenter: function() {
      var i=$(".al_list  .more").index(this)
      $(".al_list  .case-qrcode").eq(i).show();
    },
    mouseleave: function() {
      $('.case-qrcode').hide();
    }
  }, '.al_list .more');

  // $(document).on({
  //   mouseenter: function() {
  //     $(this).find('.case-qrcode').show();
  //   },
  //   mouseleave: function() {
  //     $(this).find('.case-qrcode').hide();
  //   }
  // }, '.al_list li');
}