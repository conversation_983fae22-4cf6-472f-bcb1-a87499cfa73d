@charset "utf-8";
* {
  padding: 0;
  margin: 0;
  outline: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}
body {
  min-height: 100%;
  font-family: v-sans, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  color: #1D2129;
  font-size: 16px;
}
@media (max-width: 1200px) {
  body {
    font-size: 15px;
  }
}
li {
  list-style: none;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: normal;
}
a,
a:hover,
a:focus {
  text-decoration: none;
}
input,
button {
  border-radius: 0;
}
input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
}
textarea {
  resize: none;
  overflow: auto;
}
input,
button,
textarea,
select {
  border: 0;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  background: transparent;
}
select {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  white-space: nowrap;
  -moz-appearance: none;
  appearance: none;
}
select::-ms-expand {
  display: none;
}
table {
  border-collapse: collapse;
}
img {
  vertical-align: top;
  transition: 0.5s;
}
a {
  transition: 0.5s;
}
.fl {
  float: left;
}
.fr {
  float: right;
}
.clear {
  zoom: 1;
}
.clear:after {
  content: '';
  display: block;
  clear: both;
}
.container {
  margin: 0 auto;
  width: 88%;
  max-width: 1200px;
}
@media (max-width: 1200px) {
  .container {
    width: 90%;
  }
}
@media (max-width: 767px) {
  .container {
    padding: 0 15px;
    width: 100%;
  }
}
.pt20 {
  padding-top: 20px;
}
@media (max-width: 1200px) {
  .pt20 {
    padding-top: 15px;
  }
}
@media (max-width: 767px) {
  .pt20 {
    padding-top: 10px;
  }
}
.pt30 {
  padding-top: 30px;
}
@media (max-width: 1200px) {
  .pt30 {
    padding-top: 20px;
  }
}
@media (max-width: 767px) {
  .pt30 {
    padding-top: 10px;
  }
}
.pb50 {
  padding-bottom: 50px;
}
@media (max-width: 1200px) {
  .pb50 {
    padding-bottom: 30px;
  }
}
@media (max-width: 767px) {
  .pb50 {
    padding-bottom: 20px;
  }
}
.pb80 {
  padding-bottom: 80px;
}
@media (max-width: 1200px) {
  .pb80 {
    padding-bottom: 50px;
  }
}
@media (max-width: 767px) {
  .pb80 {
    padding-bottom: 30px;
  }
}
.ptb80 {
  padding: 80px 0;
}
@media (max-width: 1200px) {
  .ptb80 {
    padding: 50px 0;
  }
}
@media (max-width: 767px) {
  .ptb80 {
    padding: 30px 0;
  }
}
.pb140 {
  padding-bottom: 140px;
}
@media (max-width: 1200px) {
  .pb140 {
    padding-bottom: 100px;
  }
}
@media (max-width: 767px) {
  .pb140 {
    padding-bottom: 30px;
  }
}
.ptb30 {
  padding-top: 30px;
  padding-bottom: 30px;
}
@media (max-width: 1200px) {
  .ptb30 {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}
@media (max-width: 767px) {
  .ptb30 {
    padding-top: 10px;
    padding-bottom: 10px;
  }
}
.pt40 {
  padding-top: 40px;
}
@media (max-width: 1200px) {
  .pt40 {
    padding-top: 30px;
  }
}
@media (max-width: 767px) {
  .pt40 {
    padding-top: 20px;
  }
}
.pt70 {
  padding-top: 70px;
}
@media (max-width: 1200px) {
  .pt70 {
    padding-top: 40px;
  }
}
@media (max-width: 767px) {
  .pt70 {
    padding-top: 20px;
  }
}
.bg_hui {
  background: #F3F3F3;
}
.bg_bai {
  background: #fff;
}
/* 公共标题 */
.title_img {
  text-align: center;
  padding: 100px 0;
}
.title_img img {
  max-height: 110px;
}
@media (max-width: 1200px) {
  .title_img {
    padding: 60px 0;
  }
  .title_img img {
    max-height: 80px;
  }
}
@media (max-width: 767px) {
  .title_img {
    padding: 30px 0;
  }
  .title_img img {
    max-height: 56px;
  }
}
/*顶部*/
#header {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 900;
  width: 100%;
  line-height: 60px;
  text-align: center;
  color: #fff;
  transition: all 0.5s;
  background: #fff;
  height: 60px;
  -moz-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  /*顶部logo*/
  /*顶部pc导航*/
}
@media (max-width: 1580px) {
  #header {
    font-size: 14px;
  }
}
@media (max-width: 1200px) {
  #header {
    position: sticky;
  }
}
#header a {
  color: #1D2129;
  transition: all 0.5s;
}
#header .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 1200px) {
  #header .container {
    padding: 0 20px;
    width: 100%;
  }
}
@media (max-width: 767px) {
  #header .container {
    padding: 0 15px;
  }
}
#header .right-box {
  display: flex;
  align-items: center;
}
#header .logo {
  display: flex;
  align-items: center;
  flex-wrap: 0;
}
#header .logo .img-box {
  display: flex;
  align-items: center;
  height: 60px;
}
#header .logo img {
  max-height: 40px;
}
@media (max-width: 767px) {
  #header .logo img {
    max-height: 34px;
  }
}
#header .nav {
  display: flex;
  align-items: center;
  font-size: 16px;
  height: auto;
}
@media (max-width: 767px) {
  #header .nav {
    display: none;
  }
}
#header .nav li {
  position: relative;
  display: flex;
  justify-content: center;
  align-content: center;
  padding: 0 20px;
}
@media (max-width: 1200px) {
  #header .nav li {
    padding: 0 10px;
  }
}
#header .nav li .tit {
  background: url(../images/jt.png) no-repeat right center;
  padding-right: 15px;
  background-size: 10px;
}
#header .nav li a {
  font-size: 16px;
  white-space: nowrap;
  cursor: pointer;
}
@media (max-width: 1320px) {
  #header .nav li a {
    font-size: 15px;
  }
}
#header .nav li:last-child {
  padding-right: 0;
}
#header .nav .li {
  padding-left: 100px;
  display: flex;
  align-items: center;
}
@media (max-width: 1200px) {
  #header .nav .li {
    padding-left: 30px;
  }
}
@media (min-width: 1000px) {
  #header .nav .li {
    padding-left: 70px;
  }
}
#header .nav .li .a_fb {
  background: #1D6AFF;
  color: #fff;
  line-height: 36px;
  border-radius: 8px;
  padding: 0 20px;
  margin-left: 20px;
}
@media (max-width: 1200px) {
  #header .nav .li .a_fb {
    margin-left: 15px;
    padding: 0 15px;
  }
}
#header .nav .li .a_fb:hover {
  background: #0048CE;
}
#header .nav ul {
  position: absolute;
  top: 99%;
  left: 50%;
  display: none;
  width: 130px;
  line-height: 46px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  background: #fff;
}
#header .nav ul li {
  border-bottom: 1px solid #fff;
  padding: 0;
  position: relative;
}
#header .nav ul li a {
  color: #000;
  font-size: 15px;
}
#header .nav ul li:hover,
#header .nav ul li.sel {
  background: #F3F3F5;
}
#header .nav ul li:hover dl,
#header .nav ul li.sel dl {
  display: block;
}
#header .nav ul li:hover a,
#header .nav ul li.sel a {
  color: #1D6AFF !important;
}
#header .nav li.on > a,
#header .nav li:hover > a {
  color: #1D6AFF;
}
#header.style2 .nav > li > a::before {
  background: #1D6AFF;
}
/*顶部移动端导航*/
#header .nav2 {
  position: absolute;
  top: 60px;
  left: 0;
  width: 100%;
  height: 0;
  line-height: 50px;
  background: #FFFFFF !important;
  overflow-y: auto;
  transition: all 0.5s;
  text-align: left;
  color: #000;
  flex-wrap: wrap;
  align-items: flex-start;
}
#header .nav2 li {
  border-bottom: 1px solid #D6D9DE;
  transition: 0.5s;
}
@media (max-width: 767px) {
  #header .nav2 li {
    padding: 0 20px;
  }
}
#header .nav2 li .title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: url(../images/jt4.png) no-repeat right center;
  background-size: 12px;
  font-size: 16px;
}
#header .nav2 li.sel .title {
  background: url(../images/jt5.png) no-repeat right center;
  background-size: 12px;
}
#header .nav2 li a {
  color: #000;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  font-size: 16px;
  width: 100%;
  display: block;
}
#header .nav2 li a:hover,
#header .nav2 li.on > a {
  color: #1D6AFF;
}
#header .nav2 li ul {
  display: none;
}
#header .nav2 li ul li {
  border: 0;
}
#header .nav2 li ul a {
  padding: 0 15px;
}
.open #header .nav2 {
  height: calc(100vh - 60px);
}
/*顶部导航开关*/
#header .switch {
  display: none;
  width: 24px;
  height: 20px;
  cursor: pointer;
}
@media (max-width: 767px) {
  #header .switch {
    display: block;
  }
}
#header .switch i {
  position: relative;
  display: block;
  height: 2px;
  background: #333333;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
#header .switch i:nth-child(1) {
  top: 0;
}
#header .switch i:nth-child(3) {
  bottom: 0;
}
#header .switch i:nth-child(2) {
  margin: 6px 0;
}
#header.style2 .c-switch i {
  background: #fff;
}
body.open #header .switch i:nth-child(2) {
  opacity: 0;
}
body.open #header .switch i:nth-child(1) {
  top: 8px;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
body.open #header .switch i:nth-child(3) {
  bottom: 8px;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
/* 底部 */
#footer {
  background: #1F2936;
  padding-top: 50px;
  overflow: hidden;
}
@media (max-width: 1200px) {
  #footer {
    padding-top: 40px;
  }
}
@media (max-width: 767px) {
  #footer {
    padding-top: 20px;
  }
}
#footer .container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
#footer .foot_one {
  display: flex;
  color: #fff;
  max-width: 60%;
}
@media (max-width: 1200px) {
  #footer .foot_one {
    max-width: 70%;
  }
}
@media (max-width: 767px) {
  #footer .foot_one {
    max-width: 100%;
    width: 100%;
    flex-wrap: wrap;
  }
}
#footer .foot_one a {
  color: #fff;
  font-size: 14px;
  line-height: 34px;
  color: #C9CDD4;
}
#footer .foot_one a:hover {
  color: #fff;
}
#footer .foot_one li {
  padding-right: 90px;
}
@media (max-width: 1200px) {
  #footer .foot_one li {
    padding-right: 60px;
  }
}
@media (max-width: 767px) {
  #footer .foot_one li {
    padding-right: 0px;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 14px;
  }
}
#footer .foot_one li h3 {
  line-height: 40px;
  font-size: 16px;
  color: #1D6AFF;
  width: 100%;
}
@media (max-width: 767px) {
  #footer .foot_one li h3 {
    font-size: 14px;
    line-height: 22px;
    color: #74A3FF;
  }
}
#footer .foot_one li p {
  padding-right: 15px;
}
@media (max-width: 767px) {
  #footer .foot_one li p a {
    font-size: 16px;
  }
}
#footer .foot_two {
  color: #fff;
  width: 240px;
}
@media (max-width: 1200px) {
  #footer .foot_two {
    width: 220px;
  }
}
#footer .foot_two h6 {
  line-height: 30px;
  font-size: 14px;
}
#footer .foot_two .tell {
  font-size: 20px;
}
#footer .foot_two dl {
  padding-top: 26px;
  display: flex;
  justify-content: space-between;
}
@media (max-width: 767px) {
  #footer .foot_two dl {
    display: none;
  }
}
#footer .foot_two dl dd {
  text-align: center;
  font-size: 12px;
  color: #C3C3C3;
  line-height: 34px;
}
#footer .foot_two dl img {
  width: 100px;
  border: 4px;
}
@media (max-width: 1200px) {
  #footer .foot_two dl img {
    width: 90px;
  }
}
#footer .foot_three {
  width: 100%;
  display: block;
  border-top: 1px solid #494D55;
  margin-top: 50px;
  padding: 15px 0;
  line-height: 30px;
  font-size: 12px;
  color: #86909C;
}
@media (max-width: 767px) {
  #footer .foot_three {
    margin-top: 20px;
    line-height: 20px;
  }
}
/* 顶部banner */
.banner {
  position: relative;
  overflow: hidden;
}
.banner img {
  width: 100%;
  display: block;
}
.banner .pc_img {
  display: block;
  width: 100%;
}
@media (max-width: 767px) {
  .banner .pc_img {
    display: none;
  }
}
.banner .yd_img {
  display: none;
  width: 100%;
}
@media (max-width: 767px) {
  .banner .yd_img {
    display: block;
  }
}
.ind_banner {
  background: linear-gradient(to bottom, #B7D5FF, #DDEEFF);
  display: block;
  width: 100%;
}
.ind_banner .next,
.ind_banner .prev {
  width: 20px;
  height: 32px;
  position: absolute;
  top: 50%;
  z-index: 100;
  transform: translate(0, -50%);
  outline: none;
  cursor: pointer;
  opacity: 0;
  transition: 0.5s;
  border-radius: 4px;
  overflow: hidden;
}
.ind_banner .next {
  right: 20px;
  background: url(../images/right.png) no-repeat center center;
  background-size: 100% 100%;
}
.ind_banner .prev {
  left: 20px;
  background: url(../images/left.png) no-repeat center center;
  background-size: 100% 100%;
}
.ind_banner:hover .next,
.ind_banner:hover .prev {
  opacity: 1;
}
.ind_banner .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
}
@media (max-width: 767px) {
  .ind_banner .container {
    padding: 10px 15px;
  }
}
@media (max-width: 1200px) {
  .ind_banner .container {
    height: 360px;
  }
}
@media (max-width: 1200px) {
  .ind_banner .container {
    height: 300px;
  }
}
@media (max-width: 767px) {
  .ind_banner .container {
    height: 200px;
    overflow: hidden;
  }
}
@media (max-width: 1200px) {
  .ind_banner .nei {
    padding-right: 40px;
  }
}
@media (max-width: 767px) {
  .ind_banner .nei {
    padding-right: 0px;
  }
}
.ind_banner .nei .title {
  font-size: 36px;
  line-height: 50px;
  color: #1D2129;
  font-weight: 600;
}
@media (max-width: 1200px) {
  .ind_banner .nei .title {
    font-size: 32px;
    line-height: 44px;
    width: 170%;
  }
}
@media (max-width: 767px) {
  .ind_banner .nei .title {
    width: 100%;
    font-size: 17px;
    line-height: 24px;
  }
}
.ind_banner .nei .desc {
  line-height: 24px;
  font-size: 16px;
  padding: 10px 0 20px 0;
}
@media (max-width: 1200px) {
  .ind_banner .nei .desc {
    width: 170%;
  }
}
@media (max-width: 767px) {
  .ind_banner .nei .desc {
    width: 100%;
    font-size: 12px;
    line-height: 20px;
    padding: 5px 0 10px 0;
  }
}
.ind_banner .nei .desc p {
  padding: 8px 0;
  font-size: 18px;
  max-width: 90%;
  line-height: 28px;
}
.ind_banner .nei .more {
  width: 200px;
  line-height: 48px;
  background: #1D6AFF;
  border-radius: 8px;
  display: inline-block;
  color: #fff;
  text-align: center;
  cursor: pointer;
}
.ind_banner .nei .more:hover {
  background: #0048CE;
}
@media (max-width: 1200px) {
  .ind_banner .nei .more {
    width: 170px;
    line-height: 44px;
  }
}
@media (max-width: 767px) {
  .ind_banner .nei .more {
    width: 110px;
    line-height: 32px;
    font-size: 15px;
    border-radius: 5px;
  }
}
.ind_banner .img {
  height: 300px;
}
@media (max-width: 1200px) {
  .ind_banner .img {
    max-height: 240px;
  }
}
@media (max-width: 767px) {
  .ind_banner .img {
    max-height: 100px;
    margin-right: -15px;
  }
}
.ind_banner .pimg {
  width: 100%;
  padding-bottom: 20px;
}
@media (max-width: 767px) {
  .ind_banner .pimg {
    padding-bottom: 10px;
  }
}
.ind_banner .img_title {
  width: 100%;
}
@media (max-width: 767px) {
  .ind_banner .img_title {
    width: 180%;
  }
}
.ind_banner .time {
  height: 63px;
  margin-bottom: -15px;
}
@media (max-width: 1200px) {
  .ind_banner .time {
    height: 50px;
    margin-bottom: -10px;
  }
}
@media (max-width: 767px) {
  .ind_banner .time {
    height: 36px;
  }
}
.ind_banner .pc_img {
  display: block;
  width: 100%;
}
@media (max-width: 767px) {
  .ind_banner .pc_img {
    display: none;
  }
}
.ind_banner .yd_img {
  display: none;
  width: 100%;
}
@media (max-width: 767px) {
  .ind_banner .yd_img {
    display: block;
  }
}
.ind_banner .type {
  height: 36px;
  margin-bottom: 10px;
}
@media (max-width: 1200px) {
  .ind_banner .type {
    height: 30px;
  }
}
@media (max-width: 767px) {
  .ind_banner .type {
    height: 26px;
  }
}
.partner_banner .container,
.case_banner .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 400px;
}
@media (max-width: 1200px) {
  .partner_banner .container,
  .case_banner .container {
    height: 360px;
  }
}
@media (max-width: 1200px) {
  .partner_banner .container,
  .case_banner .container {
    height: 300px;
  }
}
@media (max-width: 767px) {
  .partner_banner .container,
  .case_banner .container {
    height: 400px;
    overflow: hidden;
    flex-wrap: wrap;
    padding: 20px 15px;
  }
}
.partner_banner .itme1,
.case_banner .itme1 {
  background: linear-gradient(to bottom, #8EC6FF, #B8DCFF);
}
.partner_banner .itme2,
.case_banner .itme2 {
  background: linear-gradient(to bottom, #93AFFF, #BECFFF);
}
.partner_banner .itme3,
.case_banner .itme3 {
  background: linear-gradient(to bottom, #87C9E3, #ADE4EE);
}
@media (max-width: 767px) {
  .partner_banner .nei,
  .case_banner .nei {
    width: 100%;
  }
}
.partner_banner .nei .desc,
.case_banner .nei .desc {
  line-height: 24px;
  font-size: 16px;
  padding: 10px 0 20px 0;
}
@media (max-width: 767px) {
  .partner_banner .nei .desc,
  .case_banner .nei .desc {
    padding-bottom: 0;
  }
}
@media (max-width: 767px) {
  .partner_banner .nei .desc p,
  .case_banner .nei .desc p {
    padding: 4px 0;
    font-size: 14px;
    max-width: 90%;
    line-height: 22px;
  }
}
@media (max-width: 767px) {
  .partner_banner .nei .title,
  .case_banner .nei .title {
    width: 100%;
    font-size: 19px;
    line-height: 26px;
  }
}
.partner_banner .img,
.case_banner .img {
  height: 300px;
}
@media (max-width: 1200px) {
  .partner_banner .img,
  .case_banner .img {
    max-height: 240px;
  }
}
@media (max-width: 767px) {
  .partner_banner .img,
  .case_banner .img {
    max-height: 400px;
    width: 100%;
    height: auto;
    display: block;
    margin: 0;
  }
}
@media (max-width: 767px) {
  .case_banner .container {
    height: 440px;
  }
}
.case_banner .type_tit {
  line-height: 36px;
  padding: 0 18px;
  border-radius: 5px;
  color: #fff;
  font-size: 18px;
  display: inline-block;
  background: #4390E2;
  margin-bottom: 10px;
}
@media (max-width: 767px) {
  .case_banner .type_tit {
    line-height: 28px;
    font-size: 14px;
    padding: 0 10px;
  }
}
.case_banner .itme1 {
  background: linear-gradient(to bottom, #A6D3FF, #D4E1FF);
}
.case_banner .itme2 {
  background: linear-gradient(to bottom, #FFD5AD, #FFE5AB);
}
.case_banner .itme2 .type_tit {
  background: #F59614;
  color: #000;
}
.case_banner .itme3 {
  background: linear-gradient(to bottom, #C3CFE9, #DAE7F2);
}
.case_banner .itme3 .type_tit {
  background: #566E9F;
}
/*首页轮播图*/
.bannerLB {
  position: relative;
}
.bannerLB .swiper-slide {
  overflow: hidden;
  background: #fff;
}
.bannerLB .swiper-slide img {
  width: 100%;
  height: 100vh;
  object-fit: cover;
}
@media (max-width: 1200px) {
  .bannerLB .swiper-slide img {
    height: 60vh;
  }
}
.bannerLB .swiper-slide .yd {
  display: none;
}
@media (max-width: 1200px) {
  .bannerLB .swiper-slide .yd {
    display: block;
  }
}
@media (max-width: 1200px) {
  .bannerLB .swiper-slide .pc {
    display: none;
  }
}
.bannerLB .pagination {
  position: absolute;
  bottom: 10vh;
  left: 0;
  z-index: 100;
  text-align: center;
}
.bannerLB .pagination .swiper-pagination-bullet {
  width: 28px;
  height: 20px;
  background: url(../images/bg_01.png) no-repeat;
  background-size: 100% 100%;
  opacity: 1;
  margin: 0 12px;
}
@media (max-width: 1200px) {
  .bannerLB .pagination .swiper-pagination-bullet {
    width: 26px;
    height: 18px;
    margin: 0 8px;
  }
}
@media (max-width: 767px) {
  .bannerLB .pagination .swiper-pagination-bullet {
    width: 18px;
    height: 12px;
    margin: 0 5px;
  }
}
.bannerLB .pagination .swiper-pagination-bullet-active {
  background: url(../images/bg_01-sel.png) no-repeat;
  background-size: 100% 100%;
}
@media (max-width: 1200px) {
  .bannerLB .pagination {
    bottom: 50px;
  }
}
@media (max-width: 767px) {
  .bannerLB .pagination {
    bottom: 35px;
  }
}
/* 首页 */
.ind_bg {
  background: #F5F5F7;
}
.ind_one {
  display: flex;
  justify-content: space-between;
  margin-top: -40px;
  position: relative;
  z-index: 10;
}
@media (max-width: 767px) {
  .ind_one {
    margin-top: 15px;
  }
}
.ind_one li {
  width: 32%;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 20px;
  height: 80px;
  box-shadow: 0px 20px 30px 0px rgba(0, 0, 0, 0.05);
  border-radius: 12px 12px 12px 12px;
}
@media (max-width: 767px) {
  .ind_one li {
    width: 31%;
    height: 120px;
    flex-wrap: wrap;
    text-align: center;
    justify-content: center;
    padding: 10px;
  }
}
.ind_one img {
  height: 46px;
}
@media (max-width: 767px) {
  .ind_one img {
    height: 40px;
  }
}
.ind_one .txt {
  padding-left: 10px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
}
@media (max-width: 767px) {
  .ind_one .txt {
    font-size: 13px;
    width: 100%;
    padding-left: 0px;
  }
}
.ind_one .txt strong {
  font-size: 22px;
  line-height: 28px;
}
@media (max-width: 767px) {
  .ind_one .txt strong {
    font-size: 18px;
    line-height: 34px;
  }
}
.ind_title {
  font-weight: 600;
  font-size: 36px;
  color: #1D2129;
  line-height: 100px;
  text-align: center;
  letter-spacing: 2px;
}
@media (max-width: 1200px) {
  .ind_title {
    font-size: 28px;
    line-height: 80px;
  }
}
@media (max-width: 767px) {
  .ind_title {
    font-size: 20px;
    line-height: 60px;
    letter-spacing: 1px;
  }
}
.ind_two {
  display: flex;
  justify-content: center;
  margin: 0 -10px;
}
@media (max-width: 1200px) {
  .ind_two {
    margin: 0 -6px;
  }
}
@media (max-width: 767px) {
  .ind_two {
    flex-wrap: wrap;
  }
}
.ind_two li {
  width: 25%;
  padding: 0 10px;
  transition: 0.4s;
}
@media (max-width: 1200px) {
  .ind_two li {
    padding: 0 8px;
  }
}
@media (max-width: 767px) {
  .ind_two li {
    padding: 0 6px 12px 6px;
  }
}
.ind_two li:hover {
  margin-top: -15px;
}
.ind_two li:hover .nei {
  color: #1D6AFF;
}
.ind_two .nei {
  display: block;
  background: #fff;
  padding: 12px;
  box-shadow: 0px 20px 30px 0px rgba(0, 0, 0, 0.05);
  border-radius: 12px 12px 12px 12px;
  text-align: center;
  color: #333;
}
.ind_two .nei a {
  color: #333;
}
.ind_two .nei a:hover {
  color: #1D6AFF;
}
.ind_two .nei img {
  height: 60px;
}
@media (max-width: 1200px) {
  .ind_two .nei img {
    height: 50px;
  }
}
@media (max-width: 767px) {
  .ind_two .nei img {
    height: 42px;
  }
}
.ind_two .nei p {
  line-height: 24px;
  padding-top: 10px;
}
@media (max-width: 767px) {
  .ind_two .nei p {
    line-height: 20px;
    font-size: 14px;
  }
}
.ind_activity {
  position: relative;
}
.ind_activity .list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}
@media (max-width: 767px) {
  .ind_activity .list {
    margin: 0 ;
    justify-content: space-between;
  }
}
.ind_activity li {
  padding: 0 10px 20px 10px;
  width: 33.333%;
}
@media (max-width: 767px) {
  .ind_activity li {
    width: 48.2%;
    padding: 0 0 15px 0;
  }
  .ind_activity li:nth-child(9) {
    display: none;
  }
}
.ind_activity .nei {
  display: block;
  width: 100%;
  background: #FFFFFF;
  border-radius: 12px;
  padding: 12px;
  transition: 0.5s;
}
@media (max-width: 767px) {
  .ind_activity .nei {
    padding: 8px;
    border-radius: 8px;
  }
}
.ind_activity .nei:hover {
  -moz-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
}
.ind_activity .nei:hover .pimg img {
  -webkit-transform: scale(1.04);
  -moz-transform: scale(1.04);
  transform: scale(1.04);
}
.ind_activity .pimg {
  width: 100%;
  height: 240px;
  overflow: hidden;
  border-radius: 8px;
}
@media (max-width: 1200px) {
  .ind_activity .pimg {
    height: 120px;
  }
}
@media (max-width: 767px) {
  .ind_activity .pimg {
    border-radius: 6px;
    height: 90px;
  }
}
.ind_activity .pimg img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.ind_activity .time {
  font-weight: 500;
  font-size: 14px;
  color: #1D6AFF;
  line-height: 22px;
  padding: 10px 0 5px 0;
}
@media (max-width: 767px) {
  .ind_activity .time {
    font-size: 12px;
    padding: 5px 0 2px 0;
  }
}
.ind_activity .title {
  font-weight: 600;
  font-size: 16px;
  color: #1D2129;
  line-height: 24px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 48px;
  margin-bottom: 8px;
}
@media (max-width: 767px) {
  .ind_activity .title {
    font-size: 15px;
    line-height: 20px;
    height: 40px;
    margin-bottom: 5px;
  }
}
.ind_activity .address {
  padding-left: 18px;
  background: url(../images/bg_01.png) no-repeat left 4px;
  background-size: 10px;
  font-size: 14px;
  color: #4E5969;
  line-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
@media (max-width: 767px) {
  .ind_activity .address {
    font-size: 12px;
  }
}
.ind_activity .fy {
  position: absolute;
  top: -50px;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media (max-width: 767px) {
  .ind_activity .fy {
    top: -40px;
  }
}
.ind_activity .fy .next,
.ind_activity .fy .prev {
  width: 24px;
  height: 24px;
  background: #fff;
  display: inline-block;
  border-radius: 40px;
  cursor: pointer;
}
.ind_activity .fy .next {
  background: url(../images/left2.png) no-repeat center center #fff;
  background-size: 7px;
}
.ind_activity .fy .next:hover {
  background: url(../images/left2-sel.png) no-repeat center center #1D6AFF;
  background-size: 8px;
}
.ind_activity .fy .prev {
  background: url(../images/right2.png) no-repeat center center #fff;
  background-size: 7px;
}
.ind_activity .fy .prev:hover {
  background: url(../images/right2-sel.png) no-repeat center center #1D6AFF;
  background-size: 8px;
}
.ind_activity .fy .swiper-pagination-fraction {
  width: auto;
  bottom: 0;
  padding: 0 20px;
}
@media (max-width: 767px) {
  .ind_activity .fy .swiper-pagination-fraction {
    padding: 0 10px;
  }
}
.ind_more {
  line-height: 30px;
  text-align: center;
}
.ind_more a {
  padding-right: 15px;
  background: url(../images/right2.png) no-repeat right center;
  color: #000000;
}
.ind_more a:hover {
  color: #1D6AFF;
  background: url(../images/right3.png) no-repeat right center;
}
.ind_case {
  display: flex;
  background: #fff;
  border-radius: 20px;
  padding: 40px;
  align-items: flex-start;
  margin-bottom: 20px;
}
@media (max-width: 1200px) {
  .ind_case {
    padding: 30px;
  }
}
@media (max-width: 767px) {
  .ind_case {
    flex-wrap: wrap;
    background: none;
    padding: 0;
  }
}
.ind_case .list {
  margin-bottom: -24px;
  padding-right: 40px;
}
@media (max-width: 1200px) {
  .ind_case .list {
    padding-right: 30px;
    margin-bottom: 0px;
  }
}
@media (max-width: 767px) {
  .ind_case .list {
    width: 100%;
    padding: 0;
  }
}
.ind_case .list li {
  padding-bottom: 24px;
}
@media (max-width: 767px) {
  .ind_case .list li {
    padding: 10px;
    background: #fff;
    border-radius: 6px;
    margin-bottom: 15px;
  }
}
.ind_case .list .nei {
  display: flex;
  width: 100%;
  align-items: flex-start;
}
.ind_case .list .nei:hover .pimg img {
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  transform: scale(1.1);
}
.ind_case .list .nei:hover .txt .tit {
  color: #1D6AFF;
}
.ind_case .list .pimg {
  width: 160px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}
@media (max-width: 1200px) {
  .ind_case .list .pimg {
    width: 120px;
    height: 90px;
  }
}
@media (max-width: 767px) {
  .ind_case .list .pimg {
    border-radius: 5px;
  }
}
.ind_case .list .pimg img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.ind_case .list .txt {
  padding-left: 15px;
}
.ind_case .list .txt .tit {
  font-weight: 500;
  font-size: 16px;
  color: #1D2129;
  line-height: 24px;
  margin-bottom: 8px;
  font-weight: bold;
}
@media (max-width: 1200px) {
  .ind_case .list .txt .tit {
    margin-bottom: 5px;
  }
}
.ind_case .list .txt .desc {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  font-size: 14px;
  color: #4E5969;
  line-height: 22px;
}
@media (max-width: 1200px) {
  .ind_case .list .txt .desc {
    -webkit-line-clamp: 3;
    line-height: 21px;
  }
}
.ind_case .right {
  width: 38.5%;
  flex-shrink: 0;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  background: #E1E6F1;
}
@media (max-width: 1200px) {
  .ind_case .right {
    width: 42%;
  }
}
@media (max-width: 767px) {
  .ind_case .right {
    width: 100%;
  }
}
.ind_case .right .img {
  display: block;
  width: 100%;
}
.ind_case .right .txt {
  position: absolute;
  bottom: 20px;
  background: #fff;
  left: 4.5%;
  width: 91%;
  padding: 20px 30px;
  border-radius: 8px;
}
@media (max-width: 1200px) {
  .ind_case .right .txt {
    bottom: 12px;
    padding: 15px 20px;
  }
}
.ind_case .right .txt dt {
  line-height: 40px;
  font-size: 24px;
  color: #1D2129;
  font-weight: bold;
}
@media (max-width: 1200px) {
  .ind_case .right .txt dt {
    line-height: 30px;
    font-size: 20px;
  }
}
.ind_case .right .txt dd {
  line-height: 30px;
  font-size: 14px;
  color: #4E5969;
}
@media (max-width: 1200px) {
  .ind_case .right .txt dd {
    line-height: 26px;
    font-size: 13px;
  }
}
.ind_case .right .txt .bottom {
  display: flex;
  padding-top: 15px;
}
@media (max-width: 1200px) {
  .ind_case .right .txt .bottom {
    padding-top: 10px;
  }
}
.ind_case .right .txt .bottom a {
  display: inline-block;
  border: 1px solid #1D6AFF;
  width: 104px;
  border-radius: 8px;
  margin-right: 15px;
  line-height: 36px;
  text-align: center;
  color: #1D6AFF;
  font-size: 16px;
}
.ind_case .right .txt .bottom a:hover {
  background: #1D6AFF;
  color: #fff;
}
@media (max-width: 1200px) {
  .ind_case .right .txt .bottom a {
    width: 90px;
    border-radius: 6px;
    font-size: 15px;
    line-height: 32px;
  }
}
.ind_case .right .txt .bottom .a1 {
  background: #1D6AFF;
  color: #fff;
}
.ind_case .right .txt .bottom .a1:hover {
  background: #0048CE;
  color: #fff;
}
.ind_three {
  background: #E1E6F1;
  border-radius: 20px;
  padding: 20px 40px;
  margin-bottom: 80px;
}
@media (max-width: 1200px) {
  .ind_three {
    border-radius: 15px;
    padding: 10px 30px;
    margin-bottom: 60px;
  }
}
@media (max-width: 767px) {
  .ind_three {
    margin: 0 -15px;
    border-radius: 0;
    padding: 10px;
    margin-bottom: 0px;
  }
}
.ind_three .list {
  display: flex;
  margin: 0 -10px;
  flex-wrap: wrap;
  padding-bottom: 20px;
}
@media (max-width: 1200px) {
  .ind_three .list {
    margin: 0 -8px;
    padding-bottom: 10px;
  }
}
@media (max-width: 767px) {
  .ind_three .list {
    margin: 0 -3px;
    justify-content: center;
  }
}
.ind_three .list li {
  padding: 10px;
  width: 20%;
}
@media (max-width: 1200px) {
  .ind_three .list li {
    padding: 5px;
  }
}
@media (max-width: 767px) {
  .ind_three .list li {
    width: 33.33%;
  }
}
.ind_three .list a {
  display: block;
  width: 100%;
  background: #fff;
  border-radius: 8px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: 0.3s;
}
@media (max-width: 1200px) {
  .ind_three .list a {
    height: 54px;
  }
}
@media (max-width: 767px) {
  .ind_three .list a {
    height: 40px;
    padding: 5px;
    border-radius: 5px;
  }
}
.ind_three .list a img {
  max-width: 100%;
  max-height: 100%;
}
.ind_three .list a:hover {
  -moz-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  margin-top: -5px;
}
.ind_three .imgBanner {
  display: block;
  width: 100%;
  border-radius: 10px;
  margin: 10px 0;
}
@media (max-width: 767px) {
  .ind_three .imgBanner {
    display: none;
  }
}
/* 展商管理 */
.gl_banner {
  background: url(../images/bg_02.png) no-repeat center center;
  background-size: cover;
  padding: 110px 0;
  overflow: hidden;
}
@media (max-width: 1200px) {
  .gl_banner {
    padding: 70px 0;
  }
}
@media (max-width: 767px) {
  .gl_banner {
    padding: 40px 0;
  }
}
.gl_banner .title {
  font-size: 60px;
  font-weight: bold;
  line-height: 100px;
}
@media (max-width: 1200px) {
  .gl_banner .title {
    font-size: 38px;
    line-height: 60px;
  }
}
@media (max-width: 767px) {
  .gl_banner .title {
    font-size: 22px;
    line-height: 40px;
  }
}
.gl_banner .desc {
  font-size: 36px;
  color: #FE7700;
  line-height: 66px;
}
@media (max-width: 1200px) {
  .gl_banner .desc {
    font-size: 26px;
    line-height: 44px;
  }
}
@media (max-width: 767px) {
  .gl_banner .desc {
    font-size: 18px;
    line-height: 30px;
    padding-top: 5px;
  }
}
.gl_banner dl {
  display: flex;
  padding: 20px 0 60px 0;
}
@media (max-width: 1200px) {
  .gl_banner dl {
    padding: 20px 0 40px 0;
  }
}
@media (max-width: 767px) {
  .gl_banner dl {
    padding: 15px 0 30px 0;
  }
}
.gl_banner dl dd {
  min-width: 220px;
  border: 1px solid #1D6AFF;
  border-radius: 40px;
  line-height: 58px;
  text-align: center;
  margin-right: 20px;
  color: #41A5FD;
  font-size: 24px;
  font-weight: bold;
}
@media (max-width: 1200px) {
  .gl_banner dl dd {
    font-size: 18px;
    line-height: 46px;
    min-width: 150px;
  }
}
@media (max-width: 767px) {
  .gl_banner dl dd {
    font-size: 15px;
    line-height: 34px;
    min-width: 0;
    padding: 0 20px;
    white-space: nowrap;
    margin-right: 10px;
  }
}
.gl_banner .more {
  width: 380px;
  line-height: 68px;
  background: #EB7F2F;
  display: inline-block;
  text-align: center;
  font-size: 26px;
  color: #fff;
  font-weight: bold;
  letter-spacing: 2px;
}
@media (max-width: 1200px) {
  .gl_banner .more {
    font-size: 20px;
    width: 260px;
    line-height: 52px;
  }
}
@media (max-width: 767px) {
  .gl_banner .more {
    font-size: 17px;
    width: 180px;
    line-height: 40px;
  }
}
.gl_one {
  text-align: center;
  background: linear-gradient(to bottom, #F8FAFF, #fff);
}
.gl_one img {
  width: 94%;
  max-width: 1200px;
}
@media (max-width: 767px) {
  .gl_one img {
    width: 90%;
  }
}
.gl_title {
  padding: 80px 0;
  font-size: 38px;
  text-align: center;
  font-weight: bold;
  letter-spacing: 2px;
}
@media (max-width: 1200px) {
  .gl_title {
    padding: 60px 0;
    font-size: 32px;
  }
}
@media (max-width: 767px) {
  .gl_title {
    padding: 40px 0;
    font-size: 20px;
    letter-spacing: 1px;
  }
}
.gl_list {
  background: url(../images/bg_03.png) no-repeat right bottom;
  padding-bottom: 200px;
  background-size: auto 240px;
}
@media (max-width: 1200px) {
  .gl_list {
    padding-bottom: 140px;
    background-size: auto 140px;
  }
}
@media (max-width: 767px) {
  .gl_list {
    padding-bottom: 20px;
    background-size: auto 80px;
  }
}
.gl_list li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 50px;
}
@media (max-width: 767px) {
  .gl_list li {
    padding-bottom: 70px;
    align-items: flex-start;
  }
}
.gl_list li:nth-child(2n) {
  flex-direction: row-reverse;
}
.gl_list li:nth-child(2n) .nei {
  padding-left: 0;
  padding-right: 10%;
  text-align: right;
}
@media (max-width: 1200px) {
  .gl_list li:nth-child(2n) .nei {
    padding-right: 8%;
  }
}
@media (max-width: 767px) {
  .gl_list li:nth-child(2n) .nei {
    padding-right: 20px;
  }
}
.gl_list .img {
  width: 48%;
}
@media (max-width: 767px) {
  .gl_list .img {
    width: 40%;
  }
}
.gl_list .nei {
  width: 50%;
  padding-left: 10%;
}
@media (max-width: 1200px) {
  .gl_list .nei {
    padding-left: 8%;
  }
}
@media (max-width: 767px) {
  .gl_list .nei {
    width: 60%;
    padding-left: 20px;
  }
}
.gl_list .nei .title {
  font-size: 36px;
  color: #464646;
  line-height: 50px;
  font-weight: bold;
}
@media (max-width: 1200px) {
  .gl_list .nei .title {
    line-height: 46px;
    font-size: 28px;
  }
}
@media (max-width: 767px) {
  .gl_list .nei .title {
    font-size: 20px;
    line-height: 24px;
  }
}
.gl_list .nei .title_en {
  line-height: 40px;
  font-size: 30px;
  color: #333333;
  opacity: 0.1;
  font-weight: bold;
  text-transform: uppercase;
}
@media (max-width: 1200px) {
  .gl_list .nei .title_en {
    line-height: 30px;
    font-size: 22px;
    white-space: nowrap;
  }
}
@media (max-width: 767px) {
  .gl_list .nei .title_en {
    font-size: 14px;
    line-height: 20px;
  }
}
.gl_list .nei .desc {
  padding-top: 20px;
  font-size: 22px;
  color: #464646;
  line-height: 40px;
}
@media (max-width: 1200px) {
  .gl_list .nei .desc {
    font-size: 18px;
    line-height: 32px;
    padding-top: 15px;
  }
}
@media (max-width: 767px) {
  .gl_list .nei .desc {
    padding-top: 10px;
    font-size: 14px;
    line-height: 22px;
  }
}
.gl_two {
  background: url(../images/bg_04.jpg) no-repeat top center;
  background-size: cover;
  padding: 160px 0;
}
@media (max-width: 1200px) {
  .gl_two {
    padding: 100px 0;
  }
}
@media (max-width: 767px) {
  .gl_two {
    padding: 40px 0;
  }
}
.gl_two .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media (max-width: 767px) {
  .gl_two .container {
    flex-wrap: wrap;
  }
}
.gl_two .right_img {
  display: flex;
  flex-shrink: 0;
  padding-left: 50px;
}
@media (max-width: 1200px) {
  .gl_two .right_img {
    padding-left: 30px;
  }
}
@media (max-width: 767px) {
  .gl_two .right_img {
    width: 100%;
    padding-left: 0px;
    justify-content: center;
    padding: 30px 0 10px 0;
  }
}
.gl_two .right_img img {
  height: 200px;
  margin-left: 50px;
}
@media (max-width: 1200px) {
  .gl_two .right_img img {
    height: 140px;
    margin-left: 30px;
  }
}
@media (max-width: 767px) {
  .gl_two .right_img img {
    margin: 0 15px;
    height: 100px;
  }
}
.gl_two .nei .title {
  color: #464646;
  font-size: 36px;
  font-weight: 600;
}
@media (max-width: 1200px) {
  .gl_two .nei .title {
    font-size: 30px;
  }
}
@media (max-width: 767px) {
  .gl_two .nei .title {
    font-size: 20px;
  }
}
.gl_two .nei .title_en {
  color: #333333;
  opacity: 0.1;
  font-size: 28px;
  font-weight: bold;
  font-weight: 600;
  text-transform: uppercase;
}
@media (max-width: 1200px) {
  .gl_two .nei .title_en {
    font-size: 22px;
  }
}
@media (max-width: 767px) {
  .gl_two .nei .title_en {
    font-size: 15px;
  }
}
.gl_two .nei .desc {
  font-size: 20px;
  color: #474747;
  line-height: 42px;
  padding-top: 20px;
}
@media (max-width: 1200px) {
  .gl_two .nei .desc {
    line-height: 32px;
    font-size: 16px;
    padding-top: 10px;
  }
}
@media (max-width: 767px) {
  .gl_two .nei .desc {
    font-size: 14px;
    line-height: 22px;
  }
}
/* 活动报名 */
.bm_bg {
  background: #F7F7F7;
}
.bm_banner {
  background: url(../images/bg_05.png) no-repeat center center;
  background-size: cover;
  height: 900px;
  display: flex;
  justify-content: center;
  align-items: center;
}
@media (max-width: 1200px) {
  .bm_banner {
    height: 560px;
  }
  .bm_banner .img {
    height: 70px;
  }
}
@media (max-width: 767px) {
  .bm_banner {
    height: auto;
    padding: 50px 0;
  }
  .bm_banner .img {
    height: 30px;
  }
}
.bm_banner .title2 {
  padding: 15px 0 10px 0;
  line-height: 80px;
  color: #101010;
  font-size: 52px;
  font-weight: bold;
}
@media (max-width: 1200px) {
  .bm_banner .title2 {
    line-height: 60px;
    font-size: 40px;
  }
}
@media (max-width: 767px) {
  .bm_banner .title2 {
    line-height: 30px;
    font-size: 20px;
  }
}
.bm_banner .desc {
  color: #464646;
  font-size: 34px;
  line-height: 40px;
}
@media (max-width: 1200px) {
  .bm_banner .desc {
    font-size: 26px;
    line-height: 32px;
  }
}
@media (max-width: 767px) {
  .bm_banner .desc {
    font-size: 14px;
    line-height: 22px;
  }
}
.bm_banner .more {
  margin-top: 100px;
  display: block;
  width: 360px;
  text-align: center;
  background: #EB7F2F;
  line-height: 66px;
  color: #fff;
  font-size: 26px;
  letter-spacing: 2px;
}
@media (max-width: 1200px) {
  .bm_banner .more {
    margin-top: 80px;
    width: 280px;
    line-height: 56px;
    font-size: 22px;
  }
}
@media (max-width: 767px) {
  .bm_banner .more {
    margin-top: 30px;
    width: 160px;
    line-height: 38px;
    font-size: 15px;
  }
}
.bm_one {
  display: flex;
  justify-content: space-between;
  margin: 0 -10px;
  margin-top: -80px;
}
@media (max-width: 1200px) {
  .bm_one {
    margin: 0 -6px;
    margin-top: -50px;
  }
}
@media (max-width: 767px) {
  .bm_one {
    margin-top: -20px;
    flex-wrap: wrap;
  }
}
.bm_one li {
  width: 25%;
  background: #fff;
  border: 1px solid #E9E9E9;
  border-radius: 6px;
  padding: 20px;
  height: 145px;
  text-align: center;
  margin: 10px;
  transition: 0.5s;
}
.bm_one li:hover {
  -moz-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  margin-top: -15px;
}
@media (max-width: 1200px) {
  .bm_one li {
    margin: 0 6px;
    height: 100px;
    padding: 15px 0;
  }
}
@media (max-width: 767px) {
  .bm_one li {
    margin: 0;
    border-radius: 0;
    width: 50%;
    height: auto;
    margin-top: -1px;
  }
  .bm_one li:nth-child(2n) {
    border-left: 0;
  }
}
.bm_one .img {
  height: 70px;
}
@media (max-width: 1200px) {
  .bm_one .img {
    height: 50px;
  }
}
@media (max-width: 767px) {
  .bm_one .img {
    height: 40px;
  }
}
.bm_one .title {
  font-size: 18px;
  color: #727272;
  line-height: 24px;
  padding-top: 12px;
}
@media (max-width: 1200px) {
  .bm_one .title {
    padding-top: 5px;
    font-size: 15px;
  }
}
@media (max-width: 767px) {
  .bm_one .title {
    font-size: 14px;
  }
}
.bm_title {
  padding: 50px 0;
  text-align: center;
}
@media (max-width: 1200px) {
  .bm_title {
    padding: 35px 0;
  }
}
@media (max-width: 767px) {
  .bm_title {
    padding: 40px 0 20px 0;
  }
}
.bm_title .title {
  font-size: 40px;
  line-height: 60px;
  letter-spacing: 2px;
  font-weight: 600;
}
@media (max-width: 1200px) {
  .bm_title .title {
    font-size: 28px;
    line-height: 40px;
  }
}
@media (max-width: 767px) {
  .bm_title .title {
    font-size: 22px;
    line-height: 30px;
  }
}
.bm_title .title_en {
  color: #333333;
  font-size: 30px;
  text-transform: uppercase;
  opacity: 0.1;
  font-weight: 600;
}
@media (max-width: 1200px) {
  .bm_title .title_en {
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .bm_title .title_en {
    font-size: 16px;
  }
}
.bm_two {
  background: url(../images/bg_08.jpg) no-repeat;
  background-size: 100% 370px;
  padding-top: 50px;
}
@media (max-width: 1200px) {
  .bm_two {
    padding-top: 40px;
    background-size: 100% 260px;
  }
}
@media (max-width: 767px) {
  .bm_two {
    padding-top: 20px;
    background-size: 100% 130px;
  }
}
.bm_two ul {
  display: flex;
  justify-content: space-between;
}
@media (max-width: 767px) {
  .bm_two ul {
    flex-wrap: wrap;
    padding: 0 9px;
    justify-content: center;
  }
}
.bm_two li {
  width: 32%;
  border-radius: 6px;
  overflow: hidden;
  background: #fff;
  transition: 0.5s;
}
.bm_two li:hover {
  -moz-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
}
@media (max-width: 767px) {
  .bm_two li {
    border-radius: 4px;
    width: 47%;
    margin: 0 1.5%;
    margin-bottom: 15px;
  }
}
.bm_two .pimg {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 320px;
  background: #F1F4FD;
}
.bm_two .pimg .img {
  max-height: 85%;
  max-width: 85%;
}
@media (max-width: 1200px) {
  .bm_two .pimg {
    height: 220px;
  }
}
@media (max-width: 767px) {
  .bm_two .pimg {
    height: 110px;
  }
}
.bm_two .bottom {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 110px;
  flex-wrap: wrap;
  padding: 15px 0;
}
@media (max-width: 1200px) {
  .bm_two .bottom {
    height: 80px;
    padding: 10px 0;
  }
}
@media (max-width: 767px) {
  .bm_two .bottom {
    height: 60px;
    padding: 10px 0;
  }
}
.bm_two .bottom .title {
  font-size: 18px;
  width: 100%;
  text-align: center;
}
@media (max-width: 1200px) {
  .bm_two .bottom .title {
    font-size: 16px;
  }
}
@media (max-width: 767px) {
  .bm_two .bottom .title {
    font-size: 14px;
  }
}
.bm_two .bottom dl {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0 15px;
}
@media (max-width: 1200px) {
  .bm_two .bottom dl {
    padding: 0 10px;
  }
}
@media (max-width: 767px) {
  .bm_two .bottom dl {
    padding: 0;
  }
}
.bm_two .bottom dl dd {
  width: 31%;
  text-align: center;
  line-height: 28px;
  border-radius: 40px;
  font-size: 16px;
}
@media (max-width: 1200px) {
  .bm_two .bottom dl dd {
    font-size: 12px;
    line-height: 22px;
  }
}
@media (max-width: 767px) {
  .bm_two .bottom dl dd {
    border: 0 !important;
    font-size: 12px;
  }
}
.bm_two .bottom dl dd:nth-child(1) {
  color: #FFAD00;
  border: 1px solid #FFAD00;
}
.bm_two .bottom dl dd:nth-child(2) {
  color: #00CCFF;
  border: 1px solid #00CCFF;
}
.bm_two .bottom dl dd:nth-child(3) {
  color: #0D6BFD;
  border: 1px solid #0D6BFD;
}
.bm_three {
  background: url(../images/bg_09.png) no-repeat left top #fff;
  padding: 40px 0;
  overflow: hidden;
}
@media (max-width: 1200px) {
  .bm_three {
    background-size: 40%;
  }
}
@media (max-width: 767px) {
  .bm_three {
    padding: 20px 0;
    background-size: 40% 100%;
  }
}
.bm_three .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.bm_three .pimg {
  width: 47%;
}
.bm_three .pimg img {
  display: block;
  width: 100%;
}
@media (max-width: 767px) {
  .bm_three .pimg {
    width: 45%;
  }
}
.bm_three .right {
  width: 47%;
}
@media (max-width: 767px) {
  .bm_three .right {
    width: 50%;
  }
}
.bm_three .right dt {
  line-height: 30px;
  border-left: 12px solid #0D6CFE;
  font-size: 30px;
  padding-left: 15px;
  font-weight: 500;
}
@media (max-width: 1200px) {
  .bm_three .right dt {
    font-size: 22px;
    line-height: 26px;
    border-left: 10px solid #0D6CFE;
  }
}
@media (max-width: 767px) {
  .bm_three .right dt {
    font-size: 16px;
    line-height: 20px;
    border-left: 5px solid #0D6CFE;
    padding-left: 10px;
    font-weight: bold;
  }
}
.bm_three .right dd {
  padding: 30px 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
@media (max-width: 1200px) {
  .bm_three .right dd {
    padding: 15px 0;
  }
}
@media (max-width: 767px) {
  .bm_three .right dd {
    padding: 10px 0;
  }
}
.bm_three .right dd span {
  width: 45%;
  line-height: 56px;
  background: #E7F0FF;
  border-radius: 40px;
  margin: 15px 0;
  text-align: center;
  font-size: 20px;
}
@media (max-width: 1200px) {
  .bm_three .right dd span {
    margin: 10px 0;
    line-height: 44px;
    font-size: 17px;
    width: 47%;
  }
}
@media (max-width: 767px) {
  .bm_three .right dd span {
    margin: 5px 0;
    font-size: 14px;
    line-height: 30px;
  }
}
.bm_four {
  display: flex;
  justify-content: space-between;
  padding-bottom: 80px;
}
@media (max-width: 767px) {
  .bm_four {
    padding-bottom: 20px;
  }
}
.bm_four li {
  width: 48%;
  padding-bottom: 20px;
}
.bm_four li img {
  display: block;
  width: 100%;
}
.bm_four li img:hover {
  -moz-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
  -webkit-transform: scale(1.04);
  -moz-transform: scale(1.04);
  transform: scale(1.04);
}
.bm_five {
  padding: 100px 0;
  background: url(../images/bg_10.jpg) no-repeat center center;
  background-size: cover;
}
@media (max-width: 1200px) {
  .bm_five {
    padding: 70px 0;
  }
}
@media (max-width: 767px) {
  .bm_five {
    padding: 20px 0;
  }
}
.bm_five ul {
  display: flex;
  justify-content: space-between;
}
@media (max-width: 767px) {
  .bm_five ul {
    flex-wrap: wrap;
  }
}
.bm_five li {
  width: 30%;
  background: #fff;
  text-align: center;
  padding: 50px 0;
  transition: 0.4s;
}
@media (max-width: 1200px) {
  .bm_five li {
    padding: 30px 0;
    width: 31%;
  }
}
@media (max-width: 767px) {
  .bm_five li {
    width: 85%;
    display: flex;
    align-items: center;
    padding: 15px 15px;
    margin: 5px 0;
  }
  .bm_five li:nth-child(2) {
    margin-left: 7%;
  }
  .bm_five li:nth-child(3) {
    margin-left: 15%;
  }
}
.bm_five li:hover {
  -moz-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
  margin-top: -30px;
}
@media (max-width: 767px) {
  .bm_five li:hover {
    margin-top: 0px;
  }
}
.bm_five .img {
  height: 180px;
}
@media (max-width: 1200px) {
  .bm_five .img {
    height: 110px;
  }
}
@media (max-width: 767px) {
  .bm_five .img {
    flex-shrink: 0;
    width: 70px;
    height: auto;
  }
}
.bm_five .title {
  padding-top: 30px;
  font-size: 17px;
  line-height: 28px;
}
@media (max-width: 1200px) {
  .bm_five .title {
    font-size: 14px;
    line-height: 22px;
  }
}
@media (max-width: 767px) {
  .bm_five .title {
    width: 100%;
    padding-top: 0;
    text-align: left;
    padding-left: 15px;
    font-size: 13px;
  }
}
.bm_six {
  margin: 0 -25px;
  display: flex;
  flex-wrap: wrap;
}
@media (max-width: 1200px) {
  .bm_six {
    margin: 0 -15px;
  }
}
@media (max-width: 767px) {
  .bm_six {
    margin: 0 -8px;
    padding-bottom: 20px;
  }
}
.bm_six li {
  width: 33.333%;
  padding: 0 25px 50px 25px;
}
@media (max-width: 1200px) {
  .bm_six li {
    padding: 0 15px 30px 15px;
  }
}
@media (max-width: 767px) {
  .bm_six li {
    width: 100%;
    padding: 0 8px 15px 8px;
  }
}
.bm_six .bottom {
  display: block;
  width: 100%;
}
@media (max-width: 767px) {
  .bm_six .bottom {
    padding-left: 15px;
  }
}
.bm_six .nei {
  background: #fff;
  padding: 15px 15px 0 15px;
  border-radius: 6px;
  display: block;
  -moz-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.05);
  -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.05);
}
@media (max-width: 1200px) {
  .bm_six .nei {
    padding: 10px 10px 0 10px;
  }
}
@media (max-width: 767px) {
  .bm_six .nei {
    padding: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.bm_six .nei:hover {
  -moz-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
}
.bm_six .nei:hover .pimg img {
  -webkit-transform: scale(1.05);
  -moz-transform: scale(1.05);
  transform: scale(1.05);
}
.bm_six .nei:hover .title {
  color: #1D6AFF;
}
.bm_six .pimg {
  width: 100%;
  display: block;
  height: 240px;
  border-radius: 6px;
  overflow: hidden;
}
.bm_six .pimg img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
@media (max-width: 1200px) {
  .bm_six .pimg {
    height: 140px;
  }
}
@media (max-width: 767px) {
  .bm_six .pimg {
    height: 90px;
    width: 140px;
    flex-shrink: 0;
  }
}
.bm_six .title {
  margin: 17px 0 15px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  font-size: 18px;
  color: #000;
  font-weight: 600;
  transition: 0.4s;
}
@media (max-width: 1200px) {
  .bm_six .title {
    font-size: 16px;
    margin: 15px 0 10px 0;
  }
}
@media (max-width: 767px) {
  .bm_six .title {
    margin: 0 0 6px 0;
    font-size: 15px;
  }
}
.bm_six .desc {
  font-size: 16px;
  line-height: 24px;
  color: #929292;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  height: 72px;
}
@media (max-width: 1200px) {
  .bm_six .desc {
    font-size: 14px;
  }
}
@media (max-width: 767px) {
  .bm_six .desc {
    font-size: 12px;
    line-height: 20px;
    height: 40px;
    -webkit-line-clamp: 2;
  }
}
.bm_six .more {
  border-top: 1px solid #F3F3F3;
  margin-top: 10px;
  line-height: 56px;
  text-align: center;
  color: #1D6AFF;
}
@media (max-width: 1200px) {
  .bm_six .more {
    line-height: 44px;
    font-size: 15px;
  }
}
@media (max-width: 767px) {
  .bm_six .more {
    line-height: 40px;
    font-size: 14px;
    display: none;
  }
}
/* 价格 */
.jg_bg {
  background: url(../images/jg_01.png) no-repeat top center #F5F5F7;
  background-size: 100%;
}
.jg_one {
  padding: 90px 0 ;
  text-align: center;
}
@media (max-width: 1200px) {
  .jg_one {
    padding: 70px 0 50px 0;
  }
}
@media (max-width: 767px) {
  .jg_one {
    padding: 40px 0 30px 0;
  }
}
.jg_one .title {
  font-size: 38px;
  font-weight: 600;
  line-height: 70px;
  letter-spacing: 2px;
}
@media (max-width: 1200px) {
  .jg_one .title {
    font-size: 30px;
    line-height: 60px;
  }
}
@media (max-width: 767px) {
  .jg_one .title {
    letter-spacing: 1px;
    font-size: 20px;
    line-height: 30px;
  }
}
.jg_one .desc {
  font-size: 18px;
  line-height: 32px;
  padding: 0 10%;
  padding-top: 10px;
}
@media (max-width: 1200px) {
  .jg_one .desc {
    padding: 0;
    padding-top: 10px;
    font-size: 16px;
    line-height: 30px;
  }
}
@media (max-width: 767px) {
  .jg_one .desc {
    font-size: 15px;
    line-height: 28px;
  }
}
.jt_two_title {
  text-align: center;
  font-size: 38px;
  font-weight: 600;
  line-height: 110px;
  letter-spacing: 2px;
}
@media (max-width: 1200px) {
  .jt_two_title {
    font-size: 30px;
    line-height: 60px;
  }
}
@media (max-width: 767px) {
  .jt_two_title {
    letter-spacing: 1px;
    font-size: 20px;
    line-height: 50px;
  }
}
.jg_two {
  display: flex;
  margin: 0 -10px;
  padding-bottom: 10px;
}
@media (max-width: 767px) {
  .jg_two {
    flex-wrap: wrap;
    margin: 0;
  }
}
.jg_two li {
  width: 33.333%;
  padding: 10px 15px;
}
@media (max-width: 767px) {
  .jg_two li {
    width: 100%;
    padding: 10px 0;
  }
}
.jg_two li .nei {
  border-radius: 10px;
  overflow: hidden;
  background: #fff;
  height: 100%;
}
.jg_two li .img {
  display: block;
  width: 100%;
}
.jg_two .title {
  text-align: center;
  font-size: 26px;
  font-weight: bold;
  line-height: 40px;
  margin-top: -20px;
  letter-spacing: 2px;
  position: relative;
}
.jg_two .title .img_max {
  position: absolute;
  left: 64%;
  top: -45px;
  height: 50px;
}
@media (max-width: 1200px) {
  .jg_two .title .img_max {
    left: 64%;
    top: -35px;
    height: 40px;
  }
}
@media (max-width: 767px) {
  .jg_two .title .img_max {
    left: 64%;
    top: -35px;
    height: 50px;
  }
}
@media (max-width: 1200px) {
  .jg_two .title {
    font-size: 22px;
    line-height: 34px;
    margin-top: -10px;
  }
}
@media (max-width: 767px) {
  .jg_two .title {
    font-size: 26px;
    line-height: 34px;
    margin-top: -40px;
  }
}
.jg_two .desc {
  padding: 0 45px;
  margin-top: 20px;
  height: 120px;
  line-height: 28px;
  font-size: 16px;
  color: #4E5A6A;
}
@media (max-width: 1200px) {
  .jg_two .desc {
    padding: 0 20px;
    font-size: 14px;
    line-height: 24px;
  }
}
@media (max-width: 767px) {
  .jg_two .desc {
    padding: 0 30px;
    height: auto;
    padding-bottom: 15px;
  }
}
.jg_two .price {
  line-height: 50px;
  color: #000;
  font-size: 20px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
}
@media (max-width: 1200px) {
  .jg_two .price {
    font-size: 18px;
  }
}
.jg_two .price i {
  color: #000;
  font-size: 20px;
  font-weight: 700;
  margin-right: 10px;
  font-style: normal;
}
@media (max-width: 1200px) {
  .jg_two .price i {
    font-size: 18px;
  }
}
.jg_two .price strong {
  font-size: 40px;
  font-weight: 700;
}
@media (max-width: 1200px) {
  .jg_two .price strong {
    font-size: 30px;
  }
}
@media (max-width: 767px) {
  .jg_two .price strong {
    font-size: 34px;
  }
}
.jg_two .price em {
  background: #FFD24B;
  display: inline-block;
  line-height: 24px;
  border-radius: 5px;
  color: #996622;
  font-size: 14px;
  padding: 0 8px;
  margin-right: 5px;
  font-style: normal;
}
.jg_two .price span {
  padding-top: 10px;
}
.jg_two .tip {
  font-size: 13px;
  text-align: center;
  line-height: 20px;
  height: 40px;
  color: #707071;
  margin-top: -10px;
}
@media (max-width: 767px) {
  .jg_two .tip {
    height: auto;
    padding-bottom: 10px;
  }
}
.jg_two .old_price {
  text-decoration: line-through;
}
.jg_two .pbtn {
  text-align: center;
  padding-bottom: 30px;
}
@media (max-width: 1200px) {
  .jg_two .pbtn {
    padding-bottom: 30px;
  }
}
.jg_two .btn {
  margin: 0 auto;
  width: 210px;
  line-height: 50px;
  display: inline-block;
  text-align: center;
  color: #fff;
  font-size: 16px;
  border-radius: 10px;
  font-weight: bold;
  cursor: pointer;
  letter-spacing: 2px;
  transition: 0.4s;
}
@media (max-width: 1200px) {
  .jg_two .btn {
    width: 90%;
    line-height: 46px;
    font-size: 18px;
  }
}
.jg_two .bottom {
  padding: 0 45px;
  padding-bottom: 10px;
}
@media (max-width: 1200px) {
  .jg_two .bottom {
    padding: 0 20px;
    padding-bottom: 10px;
  }
}
@media (max-width: 767px) {
  .jg_two .bottom {
    padding: 0 30px;
    padding-bottom: 10px;
  }
}
.jg_two .bottom .tit {
  text-align: left;
  line-height: 62px;
  font-size: 20px;
}
@media (max-width: 1200px) {
  .jg_two .bottom .tit {
    line-height: 30px;
    font-size: 20px;
    padding: 15px 0;
  }
}
.jg_two .bottom dd {
  background: url(../images/jg_05.png) no-repeat left 7px;
  padding-left: 25px;
  color: #4E5A6A;
  background-size: 14px;
  margin-bottom: 10px;
  font-size: 15px;
  line-height: 25px;
}
.jg_two .li1 {
  /*
        .bottom{
            .tit{border-top: 1px solid #47C4DA}
            dd{
                background: url(../images/bg_11.png) no-repeat left 5px;
                background-size: 14px;
            }
        }
        */
}
.jg_two .li1 .btn {
  background: linear-gradient(to right, #8BAEFF, #5488FE);
}
.jg_two .li1 .btn:hover {
  background: linear-gradient(to right, #5488FE, #5488FE);
}
.jg_two .li2 .btn {
  background: linear-gradient(to right, #EDD8C5, #D3B09A);
  color: #533217;
}
.jg_two .li2 .btn:hover {
  background: linear-gradient(to right, #D3B09A, #D3B09A);
}
.jg_two .li3 .btn {
  background: linear-gradient(to right, #4B4D59, #252527);
  color: #EECCA9;
}
.jg_two .li3 .btn:hover {
  background: linear-gradient(to right, #252527, #252527);
}
.jg_title {
  text-align: center;
  padding: 40px 0;
  font-size: 40px;
  font-weight: 600;
  letter-spacing: 3px;
}
@media (max-width: 1200px) {
  .jg_title {
    padding: 30px 0;
    font-size: 30px;
  }
}
@media (max-width: 767px) {
  .jg_title {
    font-size: 22px;
    padding: 20px 0;
    letter-spacing: 2px;
  }
}
.jg_three {
  width: 100%;
}
.jg_three .tb {
  background: #fff;
  width: 100%;
}
.jg_three th {
  background: #fff;
  line-height: 54px;
  font-size: 20px;
}
@media (max-width: 1200px) {
  .jg_three th {
    line-height: 46px;
    font-size: 18px;
  }
}
@media (max-width: 767px) {
  .jg_three th {
    line-height: 20px;
    font-size: 15px;
    padding: 10px 5px;
    white-space: nowrap;
  }
}
.jg_three .title {
  padding: 0 !important;
  font-weight: bold;
  font-size: 19px;
  border-bottom: 2px solid #bfc3ca;
  text-align: center;
}
.jg_three .title span {
  font-size: 14px;
  display: block;
  width: 100%;
}
@media (max-width: 1200px) {
  .jg_three .title {
    font-size: 18px;
  }
}
@media (max-width: 767px) {
  .jg_three .title {
    font-size: 15px;
    line-height: 22px;
  }
}
.jg_three .xian {
  border-bottom: 2px solid #bfc3ca;
}
.jg_three tr:hover {
  background: #E9EFFD;
}
.jg_three tr:hover .title {
  background: #F5F5F7;
}
.jg_three td {
  padding: 10px 0;
  line-height: 26px ;
  font-size: 16px;
  border-bottom: 1px solid #e6e6e7;
  min-height: 50px;
  padding-left: 15px;
}
@media (max-width: 1200px) {
  .jg_three td {
    font-size: 16px;
  }
}
@media (max-width: 767px) {
  .jg_three td {
    font-size: 14px;
    padding: 7px 0 7px 10px;
    line-height: 22px;
  }
}
.jg_three .price {
  font-weight: 100;
  font-size: 15px;
  text-align: center;
  font-weight: bold;
  opacity: 0.9;
  padding: 0;
}
@media (max-width: 1200px) {
  .jg_three .price {
    font-size: 15px;
  }
}
@media (max-width: 767px) {
  .jg_three .price {
    font-size: 12px;
    padding: 0 5px;
  }
}
.jg_three .no {
  background: url(../images/tdNoCheck.png) no-repeat center center;
  background-size: 20px;
}
@media (max-width: 1200px) {
  .jg_three .no {
    background-size: 14px;
  }
}
@media (max-width: 767px) {
  .jg_three .no {
    background-size: 12px;
  }
}
.jg_three .yes {
  background: url(../images/tdCheck.png) no-repeat center center;
  background-size: 22px;
}
@media (max-width: 1200px) {
  .jg_three .yes {
    background-size: 22px;
  }
}
@media (max-width: 767px) {
  .jg_three .yes {
    background-size: 20px;
  }
}
.jg_three .a_zx {
  background: #1D6AFF;
  color: #fff;
  display: inline-block;
  padding: 0 10px;
  border-radius: 3px;
  font-weight: normal;
}
.jg_three .a_zx:hover {
  background: #0048CE;
}
.jg_four {
  border-radius: 70px;
  overflow: hidden;
  background: url(../images/jg_06.png) no-repeat left top;
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  padding: 40px 0 50px 0;
  align-items: center;
}
@media (max-width: 1200px) {
  .jg_four {
    border-radius: 30px;
    padding: 30px 0;
  }
}
@media (max-width: 767px) {
  .jg_four {
    border-radius: 15px;
    overflow: hidden;
    padding: 20px 0;
    position: relative;
    background: url(../images/jg_07.png) no-repeat left top;
    background-size: 100% 100%;
  }
}
.jg_four .ma {
  width: 170px;
  border: 10px solid #fff;
  flex-shrink: 0;
  border-radius: 10px;
}
@media (max-width: 1200px) {
  .jg_four .ma {
    width: 130px;
  }
}
@media (max-width: 767px) {
  .jg_four .ma {
    width: 100px;
    border: 5px solid #fff;
  }
}
.jg_four .txt {
  text-align: center;
  width: 100%;
  font-size: 32px;
  line-height: 56px;
  font-weight: bold;
  letter-spacing: 4px;
  padding-bottom: 20px;
}
@media (max-width: 1200px) {
  .jg_four .txt {
    font-size: 20px;
    line-height: 38px;
    letter-spacing: 2px;
  }
}
@media (max-width: 767px) {
  .jg_four .txt {
    padding-bottom: 10px;
    font-size: 16px;
    line-height: 28px;
    letter-spacing: 1px;
  }
}
.jg_table_title {
  background: #fff;
  position: fixed;
  top: 60px;
  z-index: 10;
  -moz-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  padding: 13px 0;
  width: 100%;
}
@media (max-width: 767px) {
  .jg_table_title {
    /* 手机 */
    display: none !important;
  }
}
.jg_table_title .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.jg_table_title .left {
  width: 25%;
  padding-left: 15px;
  font-size: 20px;
  font-weight: bold;
  margin-left: 18%;
}
.jg_table_title .list {
  width: 45%;
  display: flex;
  justify-content: space-between;
}
.jg_table_title .list li {
  text-align: center;
  width: 33.333%;
  line-height: 28px;
  font-size: 16px;
}
.jg_table_title .list h2 {
  font-size: 18px;
  font-weight: bold;
}
.jg_table_title .list a {
  display: inline-block;
  width: 120px;
  line-height: 34px;
  color: #fff;
  border-radius: 6px;
  font-size: 14px;
  margin-top: 3px;
  background: linear-gradient(to right, #8BAEFF, #5488FE);
}
.jg_table_title .list a:hover {
  background: linear-gradient(to right, #5488FE, #5488FE);
}
.jg_table_title .list li:nth-child(2) a {
  background: linear-gradient(to right, #EDD8C5, #D3B09A);
  color: #533217;
}
.jg_table_title .list li:nth-child(2) a:hover {
  background: linear-gradient(to right, #D3B09A, #D3B09A);
}
.jg_table_title .list li:nth-child(3) a {
  background: linear-gradient(to right, #4B4D59, #252527);
  color: #EECCA9;
}
.jg_table_title .list li:nth-child(3) a:hover {
  background: linear-gradient(to right, #252527, #252527);
}
/* 数智现场 */
.jj_one {
  width: 100%;
  background: #08287D;
  height: 600px;
  position: relative;
  overflow: hidden;
}
.jj_one .img_bg {
  height: 100%;
  width: 100%;
  object-fit: cover;
}
.jj_one .nei {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  text-align: center;
}
.jj_one .nei p img {
  max-width: 80%;
}
.jj_one p {
  width: 100%;
}
.jj_one .p1 {
  padding: 80px 0;
}
.jj_one .p3 {
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  text-align: center;
}
.jj_one .p3 img {
  display: inline-block;
  max-width: 100%;
}
.jj_title {
  width: 100%;
  text-align: center;
  padding-top: 60px;
  padding-bottom: 45px;
}
.jj_title h2 {
  width: 100%;
  color: #1D1D1D;
  font-size: 40px;
  line-height: 65px;
  letter-spacing: 4px;
}
.jj_title p {
  width: 100%;
  color: #8F8F8F;
  font-size: 18px;
  padding-top: 16px;
  padding-bottom: 30px;
  background: url(../images/onsite/bg1.png) no-repeat bottom center;
  letter-spacing: 2px;
}
.jj_two .bt {
  display: block;
  width: 100%;
  font-size: 30px;
  color: #005799;
  padding-bottom: 20px;
}
.jj_two ul {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding-bottom: 50px;
}
.jj_two ul img {
  max-width: 100%;
}
.jj_two .bottom {
  padding-bottom: 80px;
}
.jj_two .bottom {
  display: block;
  width: 100%;
  text-align: center;
}
.jj_two .bottom img {
  width: 70%;
}
.jj_three {
  background: #EEEEEE;
  width: 100%;
  padding-bottom: 30px;
}
.jj_three .list {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin: 0 -30px;
}
.jj_three .list li {
  width: 33.33%;
  padding: 0px 30px;
  padding-bottom: 44px;
}
.jj_three .list li .nei {
  display: block;
  width: 100%;
  overflow: hidden;
  border-radius: 15px;
  height: 490px;
  position: relative;
}
.jj_three .list li .nei .img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}
.jj_three .desc {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: rgba(5, 63, 143, 0.95);
  padding: 35px 40px;
  height: 260px;
  border-radius: 15px;
  text-align: center;
  color: #fff;
  transition: 0.5s;
}
.jj_three .desc h3 {
  display: block;
  width: 100%;
  font-size: 28px;
}
.jj_three .desc p {
  display: block;
  width: 100%;
  font-size: 20px;
  line-height: 35px;
  padding-top: 20px;
  letter-spacing: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}
.jj_three li:hover .desc {
  height: 350px;
}
.jj_three li:hover .desc p {
  height: 350px;
  -webkit-line-clamp: 7;
}
.jj_four {
  display: block;
  width: 100%;
  background: url(../images/onsite/bg2.jpg) no-repeat bottom center #EEEEEE;
  background-size: 100%;
  padding-bottom: 80px;
  overflow: hidden;
}
.jj_four .img1 {
  text-align: center;
  display: block;
  width: 100%;
}
.jj_four img {
  max-width: 100%;
}
.jj_four .img2 {
  text-align: center;
  padding-top: 50px;
  display: block;
  width: 100%;
}
.jj_four .shiyong {
  display: block;
  width: 100%;
  text-align: center;
  padding-top: 30px;
}
.jj_four .shiyong img {
  max-width: 30%;
}
.jj_five li {
  display: block;
  width: 100%;
  margin-bottom: 55px;
  display: flex;
  justify-content: space-between;
}
.jj_five li .img {
  width: 50%;
  height: 370px;
  position: relative;
  z-index: 1;
  object-fit: cover;
  border-radius: 0 200px 200px 0;
}
.jj_five li .nei {
  width: 60%;
  margin-left: -10%;
  border-radius: 200px 0 0 200px;
  height: 370px;
  background: url(../images/onsite/bg3.jpg) no-repeat left top;
  background-size: 100% 100%;
  padding-left: 15%;
  display: flex;
  align-items: center;
  padding-right: 10%;
}
.jj_five li h2 {
  display: block;
  width: 100%;
  color: #1D1D1D;
  font-size: 32px;
  padding-bottom: 30px;
  font-weight: normal;
}
.jj_five li .desc {
  display: block;
  width: 100%;
  color: #5F5F5F;
  font-size: 20px;
  line-height: 34px;
}
.jj_five li .desc p {
  padding: 8px 0;
}
.jj_five li:nth-child(2n) {
  flex-direction: row-reverse;
}
.jj_five li:nth-child(2n) .img {
  border-radius: 200px 0 0 200px;
}
.jj_five li:nth-child(2n) .nei {
  border-radius: 0 200px 200px 0;
  background: url(../images/onsite/bg3-2.jpg) repeat-x left top;
  background-size: 100% 100%;
  margin-left: 0;
  margin-right: -10%;
  padding-left: 0;
  padding-left: 7%;
  padding-right: 20%;
}
.jj_six {
  display: block;
  width: 100%;
  position: relative;
  object-fit: cover;
}
.jj_six .img {
  display: block;
  width: 100%;
  height: 580px;
  object-fit: cover;
}
.jj_six .swiper-button-next,
.jj_six .swiper-button-prev {
  width: 36px;
  height: 70px;
}
.jj_six .swiper-button-next {
  right: -70px;
  outline: none;
  background: url(../images/onsite/right.png) no-repeat left center;
  background-size: 100%;
}
.jj_six .swiper-button-prev {
  left: -70px;
  outline: none;
  background: url(../images/onsite/left.png) no-repeat right center;
  background-size: 100%;
}
.jj_six .desc {
  position: absolute;
  bottom: 0;
  left: 0;
  line-height: 60px;
  background: #031426;
  color: #fff;
  font-size: 18px;
  padding: 0px 25px;
  width: 100%;
}
.jj_six .swiper-pagination {
  position: absolute;
  bottom: 18px;
  right: 10px;
}
.jj_six .swiper-pagination-bullet {
  width: 16px;
  height: 16px;
  border: 2px solid #fff;
  outline: none;
  background: none;
  opacity: 1;
  margin: 0px 5px;
}
.jj_six .swiper-pagination-bullet-active {
  border: 2px solid #3B8CE5;
  background: #3B8CE5;
}
.jj_seven {
  display: block;
  width: 100%;
  background: url(../images/onsite/bg4.png) no-repeat bottom center;
  background-size: cover;
  text-align: center;
}
.jj_seven img {
  max-width: 80%;
}
.jj_seven .top {
  display: block;
  width: 100%;
  padding: 20px 0;
}
.jj_seven .bottom {
  display: block;
  width: 100%;
  padding: 60px 0 120px 0;
}
.jj_eight {
  padding-bottom: 60px;
  margin-top: -10px;
  display: flex;
  margin: 0 -15px;
  flex-wrap: wrap;
}
.jj_eight li {
  width: 33.33%;
  padding: 15px;
  display: block;
}
.jj_eight li .pimg {
  display: block;
  width: 100%;
  height: 290px;
  overflow: hidden;
}
.jj_eight li .pimg img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: 1s;
}
.jj_eight li:hover .pimg img {
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  transform: scale(1.1);
}
.jj_eight li h3 {
  display: block;
  width: 100%;
  text-align: center;
  padding: 15px 40px;
  height: 90px;
  color: #292929;
  font-size: 18px;
  line-height: 30px;
  font-weight: normal;
  letter-spacing: 2px;
}
@media screen and (min-width: 768px) and (max-width: 1200px) {
  /* 平板 */
  .jj_title {
    padding-top: 70px;
  }
  .jj_title h2 {
    font-size: 40px;
    line-height: 54px;
  }
  .jj_title p {
    font-size: 18px;
    background-size: auto 5px;
    padding-bottom: 25px;
  }
  .jj_one {
    height: 400px;
  }
  .jj_one .p1 {
    padding: 50px 0;
  }
  .jj_two {
    max-width: 96%;
  }
  .jj_two .bt {
    font-size: 24px;
  }
  .jj_two ul {
    padding-bottom: 40px;
  }
  .jj_two .bottom {
    padding-bottom: 50px;
  }
  .jj_three .list {
    margin: 0 -10px;
  }
  .jj_three .list li {
    padding: 0px 10px;
    padding-bottom: 30px;
  }
  .jj_three .list li .nei {
    height: 350px;
  }
  .jj_three .desc {
    height: 180px;
    padding: 20px;
  }
  .jj_three .desc h3 {
    font-size: 20px;
  }
  .jj_three .desc p {
    padding-top: 15px;
    font-size: 15px;
    line-height: 26px;
    letter-spacing: 1px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
  }
  .jj_four img {
    max-width: 90%;
  }
  .jj_four .img2 img {
    margin-left: -80px;
  }
  .jj_four .shiyong {
    padding-top: 15px;
  }
  .jj_four .shiyong img {
    max-width: 35%;
  }
  .jj_five {
    padding: 0px;
  }
  .jj_five li .img {
    height: 240px;
  }
  .jj_five li .nei {
    height: 240px;
  }
  .jj_five li h2 {
    font-size: 26px;
    padding-bottom: 10px;
  }
  .jj_five li .nei {
    padding-right: 20px;
  }
  .jj_five li .desc {
    font-size: 16px;
    line-height: 26px;
  }
  .jj_six {
    width: 86%;
    margin-left: 7%;
  }
  .jj_six .swiper-button-next,
  .jj_six .swiper-button-prev {
    width: 30px;
    height: 50px;
  }
  .jj_six .swiper-button-next {
    right: -50px;
  }
  .jj_six .swiper-button-prev {
    left: -50px;
  }
  .jj_six .img {
    float: left;
    width: 100%;
    height: 420px;
  }
  .jj_eight li .pimg {
    height: 200px;
  }
  .jj_eight li h3 {
    font-size: 18px;
    line-height: 28px;
    padding: 15px 20px;
    height: 70px;
  }
}
@media (max-width: 767px) {
  /* 手机 */
  .jj_title {
    padding: 0px 30px;
    padding-top: 40px;
    padding-bottom: 15px;
  }
  .jj_title h2 {
    font-size: 24px;
    line-height: 36px;
    letter-spacing: 2px;
  }
  .jj_title p {
    font-size: 14px;
    background-size: auto 5px;
    padding-bottom: 20px;
    padding-top: 8px;
    letter-spacing: 1px;
  }
  .jj_one {
    height: 180px;
  }
  .jj_one .p1 {
    padding: 20px 0;
    padding-top: 30px;
  }
  .jj_two {
    max-width: 94%;
  }
  .jj_two .bt {
    font-size: 18px;
    padding-bottom: 15px;
  }
  .jj_two ul {
    padding-bottom: 20px;
  }
  .jj_two .bottom {
    padding-bottom: 20px;
  }
  .jj_three {
    padding-bottom: 0;
    margin: 0;
    padding: 0 15px;
  }
  .jj_three .list {
    padding: 0px 8px;
    margin-bottom: -20px;
  }
  .jj_three .list li {
    padding: 0px 7px;
    padding-bottom: 20px;
    width: 50%;
  }
  .jj_three .list li .nei {
    height: 260px;
    border-radius: 10px;
  }
  .jj_three .desc {
    height: 160px;
    padding: 15px;
    border-radius: 10px;
  }
  .jj_three .desc h3 {
    font-size: 16px;
  }
  .jj_three .desc p {
    padding-top: 10px;
    font-size: 12px;
    line-height: 20px;
    letter-spacing: 1px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
  }
  .jj_four {
    padding-bottom: 20px;
  }
  .jj_four img {
    max-width: 90%;
  }
  .jj_four .img2 {
    padding-top: 20px;
  }
  .jj_four .img2 img {
    margin-left: -20px;
  }
  .jj_four .shiyong {
    padding-top: 15px;
  }
  .jj_four .shiyong img {
    max-width: 38%;
  }
  .jj_five {
    padding: 0px;
    margin: 0 -15px;
  }
  .jj_five li {
    margin-bottom: 20px;
  }
  .jj_five li .img {
    height: 160px;
    width: 40%;
  }
  .jj_five li .nei {
    height: 160px;
    width: 70%;
  }
  .jj_five li h2 {
    font-size: 16px;
    padding-bottom: 5px;
    font-weight: bold;
  }
  .jj_five li .nei {
    padding-right: 20px;
  }
  .jj_five li .desc {
    font-size: 13px;
    line-height: 22px;
  }
  .jj_five li .desc p {
    padding: 4px 0;
  }
  .jj_five li:nth-child(2n) .nei {
    padding-left: 20px;
    padding-right: 15%;
  }
  .jj_six {
    width: 82%;
    margin-left: 9%;
    margin-top: 15px;
  }
  .jj_six .swiper-button-next,
  .jj_six .swiper-button-prev {
    width: 15px;
    height: 40px;
  }
  .jj_six .swiper-button-next {
    right: -25px;
  }
  .jj_six .swiper-button-prev {
    left: -25px;
  }
  .jj_six .img {
    float: left;
    width: 100%;
    height: 240px;
  }
  .jj_six .desc {
    line-height: 40px;
    font-size: 14px;
    padding: 0px 10px;
  }
  .jj_six .swiper-pagination {
    position: absolute;
    bottom: 8px;
    right: 10px;
  }
  .jj_six .swiper-pagination-bullet {
    width: 12px;
    height: 12px;
  }
  .jj_seven .top {
    padding: 10px 0;
  }
  .jj_seven .bottom {
    padding: 10px 0 40px 0;
  }
  .jj_eight {
    padding: 0px 8px;
    margin-top: 0;
    padding-bottom: 40px;
  }
  .jj_eight li {
    width: 50%;
    padding: 7px;
  }
  .jj_eight li .pimg {
    height: 120px;
  }
  .jj_eight li h3 {
    font-size: 13px;
    line-height: 22px;
    padding: 7px 10px;
    height: 50px;
  }
}
/* 会展CRM */
.crm_banner {
  height: 540px;
  background: url(../images/img/crm1.jpg) no-repeat top center;
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
}
@media (max-width: 1200px) {
  .crm_banner {
    height: 400px;
  }
}
@media (max-width: 767px) {
  .crm_banner {
    height: auto;
    padding: 50px 0;
  }
}
.crm_banner .title {
  font-size: 60px;
  font-weight: bold;
  line-height: 90px;
  letter-spacing: 4px;
}
@media (max-width: 1200px) {
  .crm_banner .title {
    line-height: 60px;
    letter-spacing: 3px;
    font-size: 44px;
  }
}
@media (max-width: 767px) {
  .crm_banner .title {
    line-height: 30px;
    letter-spacing: 1px;
    font-size: 24px;
  }
}
.crm_banner .title2 {
  color: #FF7827;
  line-height: 60px;
  font-size: 34px;
  font-weight: bold;
}
@media (max-width: 1200px) {
  .crm_banner .title2 {
    line-height: 50px;
    font-size: 26px;
    padding-top: 5px;
  }
}
@media (max-width: 767px) {
  .crm_banner .title2 {
    line-height: 36px;
    font-size: 18px;
  }
}
.crm_banner .desc {
  padding: 10px 0 80px 0;
  font-size: 20px;
  line-height: 34px;
  color: #0B57B6;
  letter-spacing: 1px;
}
@media (max-width: 1200px) {
  .crm_banner .desc {
    font-size: 16px;
    line-height: 28px;
    padding: 5px 0 50px 0;
  }
}
@media (max-width: 767px) {
  .crm_banner .desc {
    font-size: 14px;
    line-height: 24px;
    padding: 5px 0 30px 0;
  }
}
.crm_banner .more {
  display: block;
  width: 290px;
  line-height: 54px;
  color: #fff;
  padding-left: 30px;
  background: url(../images/jt1.png) no-repeat 86% center #005EFE;
  background-size: 22px;
  font-size: 22px;
}
.crm_banner .more:hover {
  background: url(../images/jt1.png) no-repeat 92% center #005EFE;
  background-size: 22px;
}
@media (max-width: 1200px) {
  .crm_banner .more {
    width: 240px;
    line-height: 48px;
    font-size: 18px;
  }
}
@media (max-width: 767px) {
  .crm_banner .more {
    width: 200px;
    line-height: 42px;
    font-size: 16px;
    padding-left: 20px;
  }
}
.crm_one {
  padding: 70px 0;
  text-align: center;
}
@media (max-width: 1200px) {
  .crm_one {
    padding: 50px 0;
  }
}
@media (max-width: 767px) {
  .crm_one {
    padding: 30px 0;
  }
}
.crm_one .title {
  line-height: 100px;
  padding-bottom: 5px;
  background: url(../images/bg_13.png) no-repeat bottom center;
  font-size: 50px;
  letter-spacing: 3px;
}
@media (max-width: 1200px) {
  .crm_one .title {
    line-height: 80px;
    font-size: 36px;
    letter-spacing: 3px;
  }
}
@media (max-width: 767px) {
  .crm_one .title {
    line-height: 60px;
    font-size: 26px;
    background-size: auto 3px;
    letter-spacing: 2px;
  }
}
.crm_one .tit {
  line-height: 60px;
  font-size: 34px;
  padding: 20px 0;
  letter-spacing: 2px;
}
@media (max-width: 1200px) {
  .crm_one .tit {
    line-height: 40px;
    font-size: 26px;
  }
}
@media (max-width: 767px) {
  .crm_one .tit {
    line-height: 30px;
    font-size: 18px;
    padding: 15px 0;
  }
}
.crm_one .desc {
  font-size: 22px;
  line-height: 44px;
  opacity: 0.98;
  letter-spacing: 1px;
}
@media (max-width: 1200px) {
  .crm_one .desc {
    font-size: 18px;
    line-height: 36px;
  }
}
@media (max-width: 767px) {
  .crm_one .desc {
    font-size: 15px;
    line-height: 28px;
    opacity: 1;
  }
}
@media (max-width: 767px) {
  .crm_two {
    overflow: hidden;
    margin: 0 -15px;
    padding: 0 15px;
  }
}
.crm_two li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 80px;
}
.crm_two li:nth-child(2n) {
  flex-direction: row-reverse;
}
.crm_two li:nth-child(2n) .nei {
  padding-left: 0;
  padding-right: 5%;
}
@media (max-width: 1200px) {
  .crm_two li {
    padding-bottom: 60px;
  }
}
@media (max-width: 767px) {
  .crm_two li {
    flex-wrap: wrap;
    padding-bottom: 30px;
  }
}
.crm_two .pimg {
  width: 57%;
  flex-shrink: 0;
}
.crm_two .pimg img {
  width: 100%;
}
@media (max-width: 1200px) {
  .crm_two .pimg {
    width: 45%;
  }
}
@media (max-width: 767px) {
  .crm_two .pimg {
    width: 100%;
    text-align: center;
    padding-bottom: 20px;
  }
  .crm_two .pimg img {
    width: 80%;
  }
}
.crm_two .nei {
  width: 100%;
  padding-left: 5%;
}
.crm_two .nei .title {
  font-size: 40px;
  line-height: 50px;
  font-weight: 600;
  padding-bottom: 20px;
}
@media (max-width: 1200px) {
  .crm_two .nei .title {
    font-size: 26px;
    line-height: 30px;
  }
}
@media (max-width: 767px) {
  .crm_two .nei .title {
    font-size: 20px;
    line-height: 30px;
    padding-bottom: 10px;
  }
}
.crm_two .nei dd {
  padding-left: 25px;
  background: url(../images/jt2.png) no-repeat left 17px;
  background-size: 13px;
  line-height: 40px;
  font-size: 20px;
}
@media (max-width: 1200px) {
  .crm_two .nei dd {
    font-size: 16px;
    line-height: 32px;
    background: url(../images/jt2.png) no-repeat left 12px;
    padding-left: 20px;
    background-size: 11px;
  }
}
@media (max-width: 767px) {
  .crm_two .nei dd {
    font-size: 15px;
    line-height: 30px;
    background: url(../images/jt2.png) no-repeat left 12px;
    padding-left: 20px;
    background-size: 11px;
  }
}
.crm_title {
  padding: 30px 0;
  text-align: center;
}
@media (max-width: 767px) {
  .crm_title {
    padding: 30px 0 15px 0;
  }
}
.crm_title h2 {
  line-height: 64px;
  padding-bottom: 5px;
  background: url(../images/bg_13.png) no-repeat bottom center;
  font-size: 50px;
  letter-spacing: 4px;
  padding: 30px 0;
}
@media (max-width: 1200px) {
  .crm_title h2 {
    line-height: 50px;
    font-size: 36px;
    letter-spacing: 3px;
    padding: 22px 0;
  }
}
@media (max-width: 767px) {
  .crm_title h2 {
    line-height: 40px;
    font-size: 26px;
    background-size: auto 3px;
    letter-spacing: 2px;
    padding: 18px 0;
  }
}
.crm_title p {
  color: #75777C;
  font-size: 22px;
  line-height: 40px;
  letter-spacing: 2px;
  padding: 15px 0;
}
@media (max-width: 767px) {
  .crm_title p {
    font-size: 16px;
    letter-spacing: 0;
    line-height: 28px;
    padding: 10px 0;
  }
}
.crm_title .tit {
  color: #1D6AFF;
  font-size: 34px;
  line-height: 60px;
}
@media (max-width: 1200px) {
  .crm_title .tit {
    font-size: 28px;
    line-height: 40px;
  }
}
@media (max-width: 767px) {
  .crm_title .tit {
    font-size: 20px;
    line-height: 20px;
    padding-bottom: 10px;
  }
}
.crm_three {
  background: url(../images/bg_14.png) no-repeat top center #C9D5F4;
  padding: 40px 0;
}
@media (max-width: 1200px) {
  .crm_three {
    padding-top: 0;
  }
}
.crm_three .banner {
  margin-top: -50px;
}
@media (max-width: 767px) {
  .crm_three .banner {
    margin-top: -10px;
  }
}
.crm_three .nei {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 0 40px;
  padding-bottom: 20px;
}
@media (max-width: 1200px) {
  .crm_three .nei {
    padding: 0 20px;
  }
}
@media (max-width: 767px) {
  .crm_three .nei {
    padding: 0;
  }
}
.crm_three .nei .left {
  max-width: 65%;
}
@media (max-width: 767px) {
  .crm_three .nei .left {
    max-width: 100%;
    width: 100%;
  }
}
.crm_three .nei .left .title {
  font-size: 34px;
  letter-spacing: 2px;
  font-weight: bold;
  line-height: 60px;
}
@media (max-width: 1200px) {
  .crm_three .nei .left .title {
    font-size: 28px;
    line-height: 50px;
  }
}
@media (max-width: 767px) {
  .crm_three .nei .left .title {
    font-size: 22px;
    line-height: 40px;
  }
}
.crm_three .nei .right {
  padding-left: 80px;
  flex-shrink: 0;
  text-align: center;
}
@media (max-width: 767px) {
  .crm_three .nei .right {
    padding-left: 20px;
  }
}
.crm_three .nei .right .num {
  font-size: 50px;
  line-height: 60px;
  font-weight: bold;
}
@media (max-width: 1200px) {
  .crm_three .nei .right .num {
    font-size: 38px;
    line-height: 50px;
  }
}
@media (max-width: 767px) {
  .crm_three .nei .right .num {
    font-size: 30px;
    line-height: 40px;
  }
}
.crm_three .nei .desc {
  font-size: 22px;
  line-height: 44px;
}
@media (max-width: 1200px) {
  .crm_three .nei .desc {
    font-size: 18px;
    line-height: 36px;
  }
}
@media (max-width: 767px) {
  .crm_three .nei .desc {
    font-size: 15px;
    line-height: 30px;
  }
}
.crm_four {
  background: url(../images/bg_15.png) no-repeat top center;
  background-size: cover;
}
.crm_four .info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 50px;
}
@media (max-width: 767px) {
  .crm_four .info {
    flex-wrap: wrap;
    padding-bottom: 40px;
  }
}
.crm_four .info .txt {
  width: 50%;
}
@media (max-width: 767px) {
  .crm_four .info .txt {
    width: 100%;
  }
}
.crm_four .info .txt .title {
  font-size: 34px;
  line-height: 40px;
  padding-bottom: 20px;
  color: #1D6AFF;
  font-weight: bold;
}
@media (max-width: 1200px) {
  .crm_four .info .txt .title {
    font-size: 28px;
    line-height: 36px;
    padding-bottom: 15px;
  }
}
@media (max-width: 767px) {
  .crm_four .info .txt .title {
    font-size: 22px;
    line-height: 36px;
    padding-bottom: 10px;
  }
}
.crm_four .info .txt .desc {
  font-size: 20px;
  line-height: 40px;
  text-align: justify;
}
@media (max-width: 1200px) {
  .crm_four .info .txt .desc {
    font-size: 16px;
    line-height: 32px;
  }
}
@media (max-width: 767px) {
  .crm_four .info .txt .desc {
    font-size: 15px;
    line-height: 28px;
    padding-bottom: 20px;
  }
}
.crm_four .info .pimg {
  width: 40%;
  padding: 0 30px;
}
@media (max-width: 1200px) {
  .crm_four .info .pimg {
    padding: 0;
  }
}
@media (max-width: 767px) {
  .crm_four .info .pimg {
    width: 100%;
  }
}
.crm_four .info .pimg img {
  max-width: 100%;
}
.crm_four .info.info2 .txt {
  width: 57%;
  padding-right: 20%;
}
@media (max-width: 1200px) {
  .crm_four .info.info2 .txt {
    width: 50%;
    padding-right: 0%;
  }
}
@media (max-width: 767px) {
  .crm_four .info.info2 .txt {
    width: 100%;
    padding-top: 20px;
  }
}
.crm_four .list1 {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.crm_four .list1 li {
  background: url(../images/bg_16.png);
  border-radius: 20px;
  padding: 25px;
  width: 49%;
  margin-bottom: 50px;
}
@media (max-width: 767px) {
  .crm_four .list1 li {
    width: 100%;
    margin-bottom: 15px;
  }
}
.crm_four .list1 .title {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 26px;
  letter-spacing: 3px;
}
.crm_four .list1 .title img {
  height: 75px;
  width: 75px;
  border-radius: 100px;
  margin-right: 15px;
}
@media (max-width: 1200px) {
  .crm_four .list1 .title {
    font-size: 22px;
    letter-spacing: 1px;
  }
  .crm_four .list1 .title img {
    width: 56px;
    height: 56px;
  }
}
@media (max-width: 767px) {
  .crm_four .list1 .title {
    font-size: 20px;
  }
  .crm_four .list1 .title img {
    width: 50px;
    height: 50px;
  }
}
.crm_four .list1 .crm_desc {
  padding-top: 20px;
}
@media (max-width: 767px) {
  .crm_four .list1 .crm_desc {
    padding-top: 10px;
  }
}
.crm_four .list2 {
  display: flex;
  justify-content: space-between;
  padding-bottom: 30px;
}
@media (max-width: 767px) {
  .crm_four .list2 {
    margin-top: -20px;
    flex-wrap: wrap;
    padding-bottom: 30px;
  }
}
.crm_four .list2 li {
  width: 25%;
  background: url(../images/bg_16.png);
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 50px;
}
.crm_four .list2 li:nth-child(2) {
  width: 45%;
}
@media (max-width: 767px) {
  .crm_four .list2 li:nth-child(2) {
    width: 100%;
  }
}
@media (max-width: 1200px) {
  .crm_four .list2 li {
    padding: 20px;
    margin-bottom: 30px;
  }
}
@media (max-width: 767px) {
  .crm_four .list2 li {
    width: 100%;
    margin-bottom: 15px;
  }
}
.crm_bg {
  background: #E9EEFB;
  overflow: hidden;
}
.crm_tit {
  font-size: 24px;
  letter-spacing: 2px;
  line-height: 36px;
  padding-bottom: 10px;
  font-weight: bold;
}
@media (max-width: 1200px) {
  .crm_tit {
    font-size: 20px;
    letter-spacing: 1px;
    line-height: 32px;
  }
}
@media (max-width: 767px) {
  .crm_tit {
    font-size: 19px;
    line-height: 30px;
  }
}
.crm_desc {
  text-align: justify;
  font-size: 18px;
  line-height: 36px;
}
@media (max-width: 1200px) {
  .crm_desc {
    font-size: 16px;
    line-height: 30px;
  }
}
@media (max-width: 767px) {
  .crm_desc {
    font-size: 15px;
    line-height: 30px;
  }
}
.crm_five {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 0 80px 0;
}
@media (max-width: 767px) {
  .crm_five {
    padding: 0px 0 30px 0;
  }
}
.crm_five .txt {
  width: 74%;
}
.crm_five .txt .crm_tit,
.crm_five .txt .crm_desc {
  width: 96%;
}
.crm_five .txt .crm_desc {
  padding-bottom: 20px;
}
@media (max-width: 767px) {
  .crm_five .txt .crm_desc {
    padding-bottom: 10px;
  }
}
.crm_five .pimg {
  width: 24%;
}
.crm_five img {
  max-width: 100%;
}
.crm_six .pimg {
  padding: 0 30px;
}
@media (max-width: 767px) {
  .crm_six .pimg {
    padding: 0 10px;
  }
}
.crm_six .pimg img {
  display: block;
  width: 100%;
}
.crm_six .nei {
  background: #5679E3;
  border-radius: 25px;
  padding: 30px 45px;
  color: #fff;
}
@media (max-width: 1200px) {
  .crm_six .nei {
    padding: 20px 35px;
  }
}
@media (max-width: 767px) {
  .crm_six .nei {
    padding: 20px 25px;
    border-radius: 15px;
  }
}
.crm_six .nei .title {
  font-size: 30px;
  padding-bottom: 15px;
  line-height: 40px;
}
@media (max-width: 1200px) {
  .crm_six .nei .title {
    font-size: 26px;
    padding-bottom: 10px;
  }
}
@media (max-width: 767px) {
  .crm_six .nei .title {
    padding-bottom: 0;
    line-height: 30px;
    font-size: 22px;
  }
}
.crm_six ul {
  display: flex;
  justify-content: space-between;
  margin: 0 -40px;
}
@media (max-width: 1200px) {
  .crm_six ul {
    margin: 0 -30px;
  }
}
@media (max-width: 767px) {
  .crm_six ul {
    flex-wrap: wrap;
    margin: 0;
  }
}
.crm_six ul li {
  width: 33.333%;
  font-size: 18px;
  line-height: 38px;
  padding: 0 40px;
  text-align: justify;
}
.crm_six ul li:nth-child(2) {
  border-left: 1px dashed #fff;
  border-right: 1px dashed #fff;
}
@media (max-width: 767px) {
  .crm_six ul li:nth-child(2) {
    border: 0;
    border-top: 1px dashed #fff;
    border-bottom: 1px dashed #fff;
  }
}
@media (max-width: 1200px) {
  .crm_six ul li {
    padding: 0 30px;
    font-size: 16px;
    line-height: 30px;
    margin-top: 10px;
  }
}
@media (max-width: 767px) {
  .crm_six ul li {
    width: 100%;
    padding: 15px 0;
    margin-top: 0;
    font-size: 15px;
  }
}
.crm_seven {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: space-between;
  padding-bottom: 40px;
}
@media (max-width: 1200px) {
  .crm_seven {
    padding-bottom: 20px;
  }
}
@media (max-width: 767px) {
  .crm_seven {
    padding-bottom: 10px;
  }
}
.crm_seven .top {
  text-align: center;
  padding-bottom: 20px;
}
.crm_seven .top .tit {
  color: #1D6AFF;
  font-size: 34px;
  padding-right: 20px;
  font-weight: normal;
}
@media (max-width: 767px) {
  .crm_seven .top .tit {
    font-size: 24px;
    padding-right: 10px;
    line-height: 26px;
  }
}
@media (max-width: 767px) {
  .crm_seven .top {
    padding-top: 10px;
  }
}
.crm_seven .pimg {
  width: 55%;
}
@media (max-width: 1200px) {
  .crm_seven .pimg {
    width: 45%;
  }
}
@media (max-width: 767px) {
  .crm_seven .pimg {
    width: 100%;
  }
}
.crm_seven .pimg img {
  max-width: 100%;
}
.crm_seven .right {
  width: 40%;
}
@media (max-width: 1200px) {
  .crm_seven .right {
    width: 50%;
  }
}
@media (max-width: 767px) {
  .crm_seven .right {
    width: 100%;
    margin-top: -20px;
  }
}
.crm_seven .right .crm_tit {
  padding-top: 35px;
}
@media (max-width: 767px) {
  .crm_seven .right .crm_tit {
    padding-top: 25px;
  }
}
.crm_eight {
  display: flex;
  justify-content: space-between;
}
@media (max-width: 767px) {
  .crm_eight {
    flex-wrap: wrap;
  }
}
.crm_eight .txt {
  width: 50%;
}
@media (max-width: 767px) {
  .crm_eight .txt {
    width: 100%;
  }
}
.crm_eight .pimg {
  width: 45%;
}
@media (max-width: 767px) {
  .crm_eight .pimg {
    width: 100%;
    padding: 20px 0;
  }
}
.crm_eight .pimg img {
  max-width: 110%;
}
@media (max-width: 767px) {
  .crm_eight .pimg img {
    width: 100%;
  }
}
.crm_nine {
  display: flex;
  justify-content: space-between;
}
@media (max-width: 767px) {
  .crm_nine {
    flex-wrap: wrap;
  }
}
.crm_nine li {
  width: 42%;
  background: #E9EEFB;
  border-radius: 20px;
  padding: 30px;
}
.crm_nine li:nth-child(2) {
  width: 56%;
}
@media (max-width: 767px) {
  .crm_nine li {
    width: 100% !important;
    margin-bottom: 10px;
  }
}
/* 数字营销 */
.yx_banner {
  background: url(../images/bg_17.jpg) no-repeat top center;
  background-size: cover;
}
.yx_banner .title {
  font-size: 44px;
  color: #fff;
  line-height: 70px;
}
@media (max-width: 1200px) {
  .yx_banner .title {
    line-height: 60px;
    font-size: 44px;
  }
}
@media (max-width: 767px) {
  .yx_banner .title {
    line-height: 30px;
    font-size: 24px;
  }
}
.yx_banner .title2 {
  font-size: 30px;
  color: #03E6EB;
  font-weight: normal;
}
@media (max-width: 1200px) {
  .yx_banner .title2 {
    line-height: 50px;
    font-size: 26px;
  }
}
@media (max-width: 767px) {
  .yx_banner .title2 {
    line-height: 36px;
    font-size: 18px;
    padding: 8px 0;
  }
}
.yx_banner .desc {
  color: #fff;
  font-size: 16px;
}
@media (max-width: 1200px) {
  .yx_banner .desc {
    font-size: 16px;
    line-height: 28px;
  }
}
@media (max-width: 767px) {
  .yx_banner .desc {
    font-size: 14px;
    line-height: 24px;
  }
}
.yx_banner .more {
  background: url(../images/jt1.png) no-repeat 86% center #EE8045;
  background-size: 22px;
}
.yx_banner .more:hover {
  background: url(../images/jt1.png) no-repeat 92% center #FF7F00;
  background-size: 22px;
}
.yx_one {
  display: flex;
  justify-content: space-between;
  padding: 60px 0 20px 0;
}
@media (max-width: 1200px) {
  .yx_one {
    padding: 30px 0 20px 0;
  }
}
@media (max-width: 767px) {
  .yx_one {
    padding: 0;
    flex-wrap: wrap;
  }
}
.yx_one li {
  width: 32%;
  background: url(../images/bg_18.png) no-repeat left top;
  background-size: 100% 100%;
  padding: 20px 50px;
  text-align: center;
  border-radius: 10px;
  overflow: hidden;
  -moz-box-shadow: 0px 0px 10px rgba(0, 105, 255, 0.2);
  -webkit-box-shadow: 0px 0px 10px rgba(0, 105, 255, 0.2);
  box-shadow: 0px 0px 10px rgba(0, 105, 255, 0.2);
  transition: 0.3s;
}
.yx_one li:hover {
  background: url(../images/bg_19.png) no-repeat left top;
  background-size: 100% 100%;
  color: #fff;
}
@media (max-width: 1200px) {
  .yx_one li {
    padding: 20px;
  }
}
@media (max-width: 767px) {
  .yx_one li {
    width: 100%;
    padding: 30px;
    margin-top: 20px;
  }
}
.yx_one .img {
  width: 120px;
  height: 120px;
  border-radius: 100px;
  -moz-box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.15);
  -webkit-box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.15);
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.15);
}
@media (max-width: 1200px) {
  .yx_one .img {
    width: 80px;
    height: 80px;
  }
}
.yx_one .title {
  line-height: 50px;
  font-weight: bold;
  font-size: 26px;
  padding: 15px 0 10px 0;
}
@media (max-width: 1200px) {
  .yx_one .title {
    font-size: 20px;
    line-height: 32px;
  }
}
.yx_one .tit {
  font-size: 22px;
  line-height: 30px;
}
@media (max-width: 1200px) {
  .yx_one .tit {
    font-size: 18px;
    line-height: 26px;
  }
}
.yx_one .desc {
  font-size: 18px;
  padding-top: 20px;
  line-height: 34px;
}
@media (max-width: 1200px) {
  .yx_one .desc {
    font-size: 15px;
    line-height: 28px;
  }
}
.yx_two {
  padding: 10px 0;
  font-size: 22px;
  line-height: 40px;
  text-align: center;
  letter-spacing: 2px;
}
@media (max-width: 1200px) {
  .yx_two {
    font-size: 18px;
    line-height: 34px;
    letter-spacing: 1px;
  }
}
@media (max-width: 767px) {
  .yx_two {
    font-size: 15px;
    line-height: 30px;
    letter-spacing: 1px;
    padding: 20px 0 0 0;
  }
}
.yx_three {
  background: url(../images/bg_20.png) no-repeat top center;
  background-size: cover;
  padding: 20px 0;
}
@media (max-width: 767px) {
  .yx_three {
    padding: 0;
  }
}
.yx_three .crm_two {
  padding: 20px 0;
}
@media (max-width: 767px) {
  .yx_three .crm_two {
    padding-bottom: 0;
  }
}
.yx_three .crm_two .nei {
  padding-left: 10%;
}
.yx_four {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0 30px 0;
}
.yx_four .left {
  width: 38%;
}
.yx_four .right {
  width: 58%;
}
@media (max-width: 767px) {
  .yx_four {
    flex-wrap: wrap;
  }
  .yx_four .left,
  .yx_four .right {
    width: 100%;
  }
  .yx_four .left {
    margin-bottom: 20px;
  }
}
.yx_bg {
  background-color: #EAF0FD;
}
.yx_five {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0 50px 0;
}
.yx_five .left {
  width: 42%;
}
.yx_five .right {
  width: 55%;
}
@media (max-width: 767px) {
  .yx_five {
    flex-wrap: wrap;
  }
  .yx_five .left,
  .yx_five .right {
    width: 100%;
  }
  .yx_five .left {
    margin-bottom: 20px;
  }
}
.yx_six {
  padding: 30px 0 100px 0;
}
.yx_six img {
  display: block;
  width: 100%;
}
@media (max-width: 1200px) {
  .yx_six {
    padding: 20px 0 60px 0;
  }
}
@media (max-width: 767px) {
  .yx_six {
    padding: 10px 0 50px 0;
  }
}
/* 搜索列表 */
.bg_bai {
  background: #fff;
}
.ss_type {
  margin: 30px 0;
  background: #fff;
  padding: 17px 25px 20px 25px;
}
@media (max-width: 1200px) {
  .ss_type {
    padding: 12px 20px 15px 20px;
  }
}
@media (max-width: 767px) {
  .ss_type {
    margin: 20px 0;
    padding: 7px 15px 10px 15px;
  }
}
.ss_type li {
  display: flex;
  padding: 10px 0;
}
@media (max-width: 767px) {
  .ss_type li {
    padding: 5px 0;
  }
}
.ss_type .tit {
  flex-shrink: 0;
  white-space: nowrap;
  color: #515a6e;
  font-size: 14px;
  line-height: 32px;
  padding-top: 3px;
}
.ss_type dl {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.ss_type dl dd {
  margin-left: 10px;
  padding: 0 10px;
  color: #515a6e;
  font-size: 14px;
  line-height: 32px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 3px;
}
.ss_type dl dd:hover {
  color: #1D6AFF;
}
@media (max-width: 767px) {
  .ss_type dl dd {
    line-height: 30px;
  }
}
.ss_type dl .sel {
  background: #1D6AFF;
  color: #fff !important;
}
.ss_list {
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
}
@media (max-width: 767px) {
  .ss_list {
    padding-bottom: 0;
  }
}
.ss_list li {
  width: 25%;
  padding: 10px;
}
@media (max-width: 1200px) {
  .ss_list li {
    width: 33.33%;
  }
}
@media (max-width: 767px) {
  .ss_list li {
    padding: 0px;
    width: 100%;
    margin-bottom: 15px;
  }
}
.ss_list .nei {
  display: block;
  border-radius: 5px;
  border: 1px solid #E9E9E9;
  overflow: hidden;
}
.ss_list .nei:hover {
  border: 1px solid #1D6AFF;
}
.ss_list .nei:hover .pimg img {
  -webkit-transform: scale(1.05);
  -moz-transform: scale(1.05);
  transform: scale(1.05);
}
.ss_list .nei:hover .txt .title {
  color: #1D6AFF;
}
@media (max-width: 767px) {
  .ss_list .nei {
    display: flex;
  }
}
.ss_list .pimg {
  width: 100%;
  height: 180px;
  overflow: hidden;
}
@media (max-width: 1200px) {
  .ss_list .pimg {
    height: 140px;
  }
}
@media (max-width: 767px) {
  .ss_list .pimg {
    width: 40%;
    height: 100px;
    flex-wrap: wrap;
    flex-shrink: 0;
  }
}
.ss_list .pimg img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.ss_list .txt {
  padding: 12px 15px;
}
@media (max-width: 767px) {
  .ss_list .txt {
    width: 60%;
    padding: 5px 0 5px 10px;
  }
}
.ss_list .txt .date {
  color: #999;
  font-size: 12px;
  padding-left: 20px;
  background: url(../images/time.png) no-repeat left center;
  background-size: 16px;
  line-height: 20px;
}
.ss_list .txt .wei {
  padding-left: 20px;
  background: url(../images/wei.png) no-repeat left 2px;
  background-size: 16px;
  font-size: 12px;
  line-height: 20px;
  color: #999;
}
@media (max-width: 767px) {
  .ss_list .txt .wei {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.ss_list .txt .title {
  height: 44px;
  line-height: 20px;
  font-size: 14px;
  color: #000;
  margin: 10px 0 6px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
@media (max-width: 767px) {
  .ss_list .txt .title {
    margin: 5px 0 0px 0;
    padding-right: 6px;
  }
}
/* 案例 */
.al_bg {
  background: #F3F3F3;
}
.al_banner {
  display: block;
  width: 100%;
}
.al_banner .img {
  display: block;
  width: 100%;
}
.al_banner .nei {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.al_banner .nei .zi {
  width: 550px;
}
@media (max-width: 1200px) {
  .al_banner .nei .zi {
    width: 350px;
  }
}
@media (max-width: 767px) {
  .al_banner .nei .zi {
    width: 150px;
  }
}
.al_banner .next,
.al_banner .prev {
  width: 20px;
  height: 32px;
  position: absolute;
  top: 50%;
  z-index: 100;
  transform: translate(0, -50%);
  outline: none;
  cursor: pointer;
  opacity: 0;
  transition: 0.5s;
  border-radius: 4px;
  overflow: hidden;
}
.al_banner .next {
  right: 20px;
  background: url(../images/right.png) no-repeat center center;
  background-size: 100% 100%;
}
.al_banner .prev {
  left: 20px;
  background: url(../images/left.png) no-repeat center center;
  background-size: 100% 100%;
}
.al_banner:hover .next,
.al_banner:hover .prev {
  opacity: 1;
}
.al_tishi {
  font-size: 16px;
  padding: 20px 0 10px 0;
  line-height: 30px;
  color: #515a6e;
}
@media (max-width: 1200px) {
  .al_tishi {
    font-size: 14px;
    line-height: 20px;
  }
}
.al_main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
@media (max-width: 767px) {
  .al_main {
    flex-wrap: wrap;
  }
}
.al_main .al_left {
  width: 66%;
}
@media (max-width: 1200px) {
  .al_main .al_left {
    width: 65%;
  }
}
@media (max-width: 767px) {
  .al_main .al_left {
    width: 100%;
  }
}
.al_main .al_right {
  width: 32%;
}
@media (max-width: 1200px) {
  .al_main .al_right {
    width: 32%;
  }
}
@media (max-width: 767px) {
  .al_main .al_right {
    width: 100%;
  }
}
.al_list {
  margin: 0 -10px;
  display: flex;
  flex-wrap: wrap;
}
@media (max-width: 1200px) {
  .al_list {
    margin: 0 -7px;
  }
}
.al_list li {
  padding: 10px;
  width: 33.33%;
}
@media (max-width: 1200px) {
  .al_list li {
    width: 50%;
    padding: 7px;
  }
}
.al_list .nei {
  display: block;
  background: #fff;
  border-radius: 5px;
  padding: 10px;
}
.al_list .nei:hover {
  -moz-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
}
@media (max-width: 767px) {
  .al_list .nei {
    width: 100%;
  }
}
.al_list .txt {
  width: 100%;
}
.al_list .pimg {
  display: block;
  width: 100%;
  height: 170px;
  border-radius: 5px;
  overflow: hidden;
}
@media (max-width: 767px) {
  .al_list .pimg {
    height: 90px;
    flex-shrink: 0;
  }
}
.al_list .pimg img {
  display: block;
  width: 100%;
  height: 100%;
}
.al_list .title {
  margin: 8px 0 4px 0;
  line-height: 32px;
  height: 32px;
  color: #353535;
  font-size: 16px;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
@media (max-width: 767px) {
  .al_list .title {
    margin: 0;
  }
}
.al_list .desc {
  color: #999999;
  font-size: 13px;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  height: 80px;
}
.al_list .type {
  display: flex;
  overflow: hidden;
  height: 30px;
  padding-top: 8px;
}
.al_list .type dd {
  white-space: nowrap;
  flex-shrink: 0;
  line-height: 20px;
  background: #E5E6EB;
  border-radius: 4px;
  padding: 0 8px;
  margin-right: 6px;
  font-size: 12px;
  color: #61616D;
}
.al_list .more {
  cursor: pointer;
  border-top: 1px solid #EEEEEE;
  color: #1D6AFF;
  text-align: center;
  font-size: 14px;
  margin-top: 10px;
  padding-top: 10px;
  line-height: 24px;
}
.al_one {
  display: block;
  width: 100%;
}
@media (max-width: 767px) {
  .al_one {
    margin-top: 30px;
  }
}
.al_one img {
  display: block;
  width: 100%;
}
.al_one .btn {
  text-align: center;
  padding: 15px 0;
}
.al_one .btn a {
  display: inline-block;
  width: 38%;
  line-height: 28px;
  border: 1px solid #1D6AFF;
  background: #1D6AFF;
  font-size: 14px;
  color: #fff;
  margin: 0 2%;
  border-radius: 30px;
}
.al_one .btn a:hover {
  opacity: 0.8;
}
.al_one .btn .a2 {
  color: #1D6AFF;
  background: #fff;
}
.al_two {
  border-bottom: 1px solid #DFDFDF;
  background: url(../images/bg_21.png) no-repeat left center;
  background-size: 4px 15px;
  padding-left: 18px;
  font-size: 20px;
  color: #0348D9;
  line-height: 60px;
}
@media (max-width: 767px) {
  .al_two {
    line-height: 50px;
  }
}
.al_three {
  padding: 15px;
  background: #fff;
  border-radius: 8px;
}
.al_three li {
  padding: 15px 0;
}
@media (max-width: 767px) {
  .al_three li {
    padding: 10px 0;
  }
}
.al_three .nei {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.al_three .nei:hover {
  background: #fff;
}
.al_three .nei:hover .txt .tit {
  color: #1D6AFF;
}
.al_three .img {
  width: 120px;
  height: 90px;
  border-radius: 8px;
  object-fit: cover;
  flex-shrink: 0;
}
.al_three .txt {
  padding-left: 10px;
}
.al_three .txt .tit {
  font-size: 16px;
  font-weight: bold;
  color: #000;
  transition: 0.4s;
  line-height: 26px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.al_three .txt .desc {
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  color: #656565;
  line-height: 22px;
  margin-top: 10px;
}
.case_one {
  background: linear-gradient(to bottom, #FFE9CA, #FCF5ED);
  padding: 30px;
  border-radius: 10px;
  margin: 60px 0 80px 0;
}
@media (max-width: 1200px) {
  .case_one {
    padding: 20px;
    margin: 40px 0 50px 0;
  }
}
@media (max-width: 767px) {
  .case_one {
    padding: 15px;
    margin: 0 -15px;
    border-radius: 0;
    margin-bottom: 30px;
  }
}
.case_title {
  padding: 20px 0;
  text-align: center;
}
@media (max-width: 767px) {
  .case_title {
    padding: 15px 0;
  }
}
.case_title h2 {
  background: url(../images/bg_26.png) no-repeat center center;
  background-size: auto 46px;
  line-height: 60px;
  font-weight: bold;
  font-size: 36px;
  letter-spacing: 3px;
}
@media (max-width: 1200px) {
  .case_title h2 {
    line-height: 50px;
    background-size: auto 40px;
    font-size: 30px;
  }
}
@media (max-width: 767px) {
  .case_title h2 {
    line-height: 40px;
    background-size: auto 32px;
    font-size: 24px;
  }
}
.case_title p {
  color: #BD9159;
  font-size: 14px;
  line-height: 28px;
}
.case_two {
  width: 100%;
  height: 160px;
  flex-shrink: 0;
  border-radius: 10px;
  position: relative;
  background: #E1E6F1;
  margin-top: 80px;
  padding: 0 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media (max-width: 1200px) {
  .case_two {
    height: 140px;
    padding: 0 30px;
    margin-top: 50px;
  }
}
@media (max-width: 767px) {
  .case_two {
    display: none;
  }
}
.case_two .img {
  display: block;
  width: 36%;
  position: absolute;
  bottom: 0;
  right: 40px;
}
@media (max-width: 1200px) {
  .case_two .img {
    width: 40%;
    right: 20px;
  }
}
.case_two .txt {
  border-radius: 8px;
}
@media (max-width: 1200px) {
  .case_two .txt {
    bottom: 12px;
    padding: 15px 0px;
  }
}
.case_two .txt dt {
  line-height: 40px;
  font-size: 24px;
  color: #1D2129;
  font-weight: bold;
}
@media (max-width: 1200px) {
  .case_two .txt dt {
    line-height: 30px;
    font-size: 20px;
  }
}
.case_two .txt dd {
  line-height: 30px;
  font-size: 14px;
  color: #4E5969;
}
@media (max-width: 1200px) {
  .case_two .txt dd {
    line-height: 26px;
    font-size: 13px;
  }
}
.case_two .txt .bottom {
  display: flex;
  padding-top: 15px;
}
@media (max-width: 1200px) {
  .case_two .txt .bottom {
    padding-top: 10px;
  }
}
.case_two .txt .bottom a {
  display: inline-block;
  border: 1px solid #1D6AFF;
  width: 104px;
  border-radius: 8px;
  margin-right: 15px;
  line-height: 36px;
  text-align: center;
  color: #1D6AFF;
  font-size: 16px;
}
.case_two .txt .bottom a:hover {
  background: #1D6AFF;
  color: #fff;
}
@media (max-width: 1200px) {
  .case_two .txt .bottom a {
    width: 90px;
    border-radius: 6px;
    font-size: 15px;
    line-height: 32px;
  }
}
.case_two .txt .bottom .a1 {
  background: #1D6AFF;
  color: #fff;
}
.case_two .txt .bottom .a1:hover {
  background: #0048CE;
  color: #fff;
}
.case_three {
  display: block;
  bottom: 20px;
  background: url(../images/bg_27.png) no-repeat right top #E1E6F1;
  background-size: auto 100%;
  width: 100%;
  padding: 20px 30px;
  border-radius: 8px;
  margin-top: 100px;
  margin-bottom: 40px;
}
@media (max-width: 1200px) {
  .case_three {
    bottom: 12px;
    padding: 15px 20px;
  }
}
@media (max-width: 767px) {
  .case_three {
    margin-top: 40px;
    margin-bottom: 40px;
  }
}
.case_three dt {
  line-height: 40px;
  font-size: 24px;
  color: #1D2129;
  font-weight: bold;
}
@media (max-width: 1200px) {
  .case_three dt {
    line-height: 30px;
    font-size: 20px;
  }
}
.case_three dd {
  line-height: 30px;
  font-size: 14px;
  color: #4E5969;
}
@media (max-width: 1200px) {
  .case_three dd {
    line-height: 26px;
    font-size: 13px;
  }
}
.case_three .bottom {
  display: flex;
  padding-top: 15px;
}
@media (max-width: 1200px) {
  .case_three .bottom {
    padding-top: 10px;
  }
}
.case_three .bottom a {
  display: inline-block;
  border: 1px solid #1D6AFF;
  width: 104px;
  border-radius: 8px;
  margin-right: 15px;
  line-height: 36px;
  text-align: center;
  color: #1D6AFF;
  font-size: 16px;
  background: #fff;
}
.case_three .bottom a:hover {
  background: #1D6AFF;
  color: #fff;
}
@media (max-width: 1200px) {
  .case_three .bottom a {
    width: 90px;
    border-radius: 6px;
    font-size: 15px;
    line-height: 32px;
  }
}
.case_three .bottom .a1 {
  background: #1D6AFF;
  color: #fff;
}
.case_three .bottom .a1:hover {
  background: #0048CE;
  color: #fff;
}
/* 服务商 */
.fw_main {
  padding: 30px 0;
}
.fw_main .fw_left {
  width: 100%;
}
@media (max-width: 767px) {
  .fw_main {
    padding-bottom: 0;
  }
}
.fw_title {
  text-align: center;
  padding-bottom: 20px;
  margin-top: -20px;
}
@media (max-width: 767px) {
  .fw_title {
    margin-top: -10px;
  }
}
.fw_title h2 {
  line-height: 60px;
  font-size: 32px;
  font-weight: bold;
  color: #1D2129;
  letter-spacing: 3px;
}
@media (max-width: 1200px) {
  .fw_title h2 {
    font-size: 28px;
    line-height: 50px;
  }
}
@media (max-width: 767px) {
  .fw_title h2 {
    font-size: 22px;
    line-height: 40px;
  }
}
.fw_title p {
  font-size: 14px;
  color: #4E5969;
  line-height: 30px;
}
@media (max-width: 767px) {
  .fw_title p {
    line-height: 20px;
  }
}
.fw_list {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.fw_list li {
  width: 49%;
  padding-bottom: 20px;
}
@media (max-width: 767px) {
  .fw_list li {
    padding-bottom: 15px;
    width: 100%;
  }
}
.fw_list .nei {
  display: block;
  width: 100%;
  padding: 18px;
  background: #fff;
  border-radius: 6px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
}
.fw_list .nei:hover {
  -moz-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
}
.fw_list .nei:hover .more {
  background: #1D6AFF;
  color: #fff;
}
@media (max-width: 767px) {
  .fw_list .nei {
    border-radius: 0;
  }
}
.fw_list .plogo {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  overflow: hidden;
}
.fw_list .plogo img {
  max-width: 100%;
  max-height: 100%;
}
@media (max-width: 767px) {
  .fw_list .plogo {
    width: 100px;
    height: 100px;
  }
}
.fw_list .txt {
  width: 100%;
  padding: 0 20px;
}
@media (max-width: 767px) {
  .fw_list .txt {
    padding: 0 0 0 15px;
  }
}
.fw_list .txt .name {
  font-size: 18px;
  font-weight: 700;
  line-height: 30px;
  margin-bottom: 10px;
  color: #000;
}
@media (max-width: 767px) {
  .fw_list .txt .name {
    font-size: 16px;
    margin-bottom: 5px;
  }
}
.fw_list .txt .type {
  color: #000;
  font-size: 15px;
  line-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.fw_list .txt .desc {
  font-size: 14px;
  color: #656565;
  line-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-top: 5px;
}
@media (max-width: 767px) {
  .fw_list .txt .desc {
    font-size: 12px;
    line-height: 20px;
  }
}
.fw_list .more {
  display: block;
  color: #1D6AFF;
  flex-shrink: 0;
  text-align: center;
  font-size: 15px;
  transition: 0.2s;
  width: 120px;
  height: 32px;
  line-height: 30px;
  border: 1px solid #1D6AFF;
  border-radius: 5px;
  margin-top: 10px;
}
@media (max-width: 1200px) {
  .fw_list .more {
    display: none;
  }
}
.fw_right {
  background: #E1E6F1;
  border-radius: 20px;
  width: 100%;
  margin-top: 20px;
  padding: 40px 40px 0 40px;
}
.fw_right .di_img {
  display: block;
  width: 100%;
  border-radius: 8px;
}
@media (max-width: 1200px) {
  .fw_right {
    position: relative;
    top: 0;
  }
}
@media (max-width: 767px) {
  .fw_right {
    width: 100%;
    padding: 15px;
    border-radius: 0;
    padding-bottom: 40px;
  }
}
.fw_right .tit {
  padding-bottom: 40px;
  font-size: 34px;
  font-weight: bold;
  text-align: center;
  letter-spacing: 3px;
  line-height: 50px;
}
@media (max-width: 1200px) {
  .fw_right .tit {
    font-size: 26px;
  }
}
@media (max-width: 767px) {
  .fw_right .tit {
    font-size: 22px;
    line-height: 50px;
    padding-bottom: 15px;
    letter-spacing: 3px;
  }
}
.fw_info {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  align-items: flex-start;
}
@media (max-width: 1200px) {
  .fw_info {
    flex-wrap: wrap;
  }
}
.fw_info .right_img {
  border-radius: 8px;
  width: 65%;
}
.fw_info .right_img img {
  display: block;
  width: 100%;
  border-radius: 12px;
  margin-bottom: 40px;
}
@media (max-width: 1200px) {
  .fw_info .right_img {
    width: 100%;
  }
  .fw_info .right_img img {
    border-radius: 8px;
    margin-bottom: 25px;
  }
}
@media (max-width: 767px) {
  .fw_info .right_img img {
    border-radius: 8px;
    margin-bottom: 15px;
  }
}
.fw_info .nei {
  width: 32%;
  background: #fff;
  background-size: 100%;
  border-radius: 10px;
  padding: 15px 25px;
  margin-right: 10px;
  margin-bottom: 40px;
}
@media (max-width: 1200px) {
  .fw_info .nei {
    width: 100%;
    margin-right: 0;
    margin-bottom: 25px;
  }
}
@media (max-width: 767px) {
  .fw_info .nei {
    margin-bottom: 0;
    padding: 15px;
  }
}
.fw_info .nei .title {
  line-height: 44px;
  color: #000;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
}
.fw_info .nei h3 {
  font-size: 15px;
  line-height: 36px;
}
.fw_info .nei h3 i {
  padding-right: 5px;
  font-style: normal;
  color: red;
}
.fw_info .nei .txt {
  width: 100%;
  display: block;
  border: 1px solid #D7DDFF;
  height: 38px;
  line-height: 36px;
  padding-left: 10px;
  font-size: 15px;
  color: #000;
  border-radius: 4px;
  margin-bottom: 6px;
}
.fw_info .nei .btn {
  display: block;
  background: #1D6AFF;
  color: #fff;
  font-size: 16px;
  margin-top: 20px;
  line-height: 40px;
  width: 100%;
  margin-bottom: 5px;
  border-radius: 8px;
}
.fw_info .nei .btn:hover {
  background: #0048CE;
}
.fw_banner {
  padding: 70px 0;
}
.fw_banner .pc_img {
  display: block;
  width: 100%;
  border-radius: 20px;
}
.fw_banner .yd_img {
  display: none;
  width: 100%;
  border-radius: 10px;
}
@media (max-width: 1200px) {
  .fw_banner {
    padding: 40px 0;
  }
}
@media (max-width: 767px) {
  .fw_banner {
    padding: 15px 0;
  }
  .fw_banner .pc_img {
    display: none;
  }
  .fw_banner .yd_img {
    display: block;
  }
}
/* 详情 */
.xq_info {
  background: #fff;
  margin: 60px 0px 40px 0;
  display: flex;
  align-items: flex-start;
}
@media (max-width: 767px) {
  .xq_info {
    margin: 0 -15px;
    flex-wrap: wrap;
    padding: 20px  15px;
  }
}
.xq_info .pimg {
  width: 420px;
  height: 280px;
  flex-shrink: 0;
}
@media (max-width: 1200px) {
  .xq_info .pimg {
    width: 320px;
    height: 220px;
  }
}
@media (max-width: 767px) {
  .xq_info .pimg {
    width: 100%;
  }
}
.xq_info .pimg img {
  object-fit: cover;
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  margin-top: -40px;
  margin-left: -20px;
}
@media (max-width: 767px) {
  .xq_info .pimg img {
    margin: 0;
  }
}
.xq_info .nei {
  width: 100%;
  padding: 30px 30px 30px 10px;
}
@media (max-width: 767px) {
  .xq_info .nei {
    padding: 15px 0 0 0;
  }
}
.xq_info .nei .title {
  font-size: 22px;
  line-height: 30px;
  padding: 10px 0;
  color: #3A4150;
  font-weight: bold;
  letter-spacing: 1px;
}
@media (max-width: 767px) {
  .xq_info .nei .title {
    font-size: 18px;
    line-height: 28px;
  }
}
.xq_info .nei dd {
  padding: 5px 0;
  line-height: 24px;
  font-size: 15px;
  opacity: 0.8;
  color: #000;
}
@media (max-width: 767px) {
  .xq_info .nei dd {
    font-size: 13px;
    line-height: 22px;
    padding: 5px 0;
  }
}
.xq_info .nei .user {
  padding: 10px 0;
  display: flex;
  align-items: center;
}
.xq_info .nei .user img {
  width: 36px;
  height: 36px;
  object-fit: cover;
  border-radius: 40px;
  margin-right: 10px;
}
.xq_info .nei .user span {
  font-weight: bold;
  color: #000;
  opacity: 0.5;
  font-size: 15px;
}
.xq_info .bottom {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  font-size: 14px;
  width: 160%;
  margin-left: -60%;
}
@media (max-width: 767px) {
  .xq_info .bottom {
    width: 100%;
    margin: 0;
    padding: 10px 0;
    justify-content: space-between;
  }
}
.xq_info .bottom .a_bm {
  padding: 0 15px;
  line-height: 34px;
  border-radius: 3px;
  background: #1D6AFF;
  color: #fff;
  font-size: 14px;
  margin-right: 20px;
}
.xq_info .bottom .a_bm:hover {
  opacity: 0.8;
}
.xq_desc {
  background: #fff;
  padding: 40px 30px;
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .xq_desc {
    margin: 0 -15px;
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 25px 15px;
  }
}
.xq_desc .title {
  font-size: 20px;
  font-weight: bold;
  padding-bottom: 20px;
}
@media (max-width: 767px) {
  .xq_desc .title {
    font-size: 18px;
    padding-bottom: 15px;
  }
}
.xq_desc .desc {
  font-size: 16px;
  line-height: 36px;
  color: #000;
  opacity: 0.8;
  padding: 0 30px;
}
@media (max-width: 767px) {
  .xq_desc .desc {
    padding: 0 ;
    font-size: 14px;
    line-height: 30px;
  }
}
/* 分页 */
.fenye {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0 60px 0;
}
@media (max-width: 1200px) {
  .fenye {
    padding: 20px 0 40px 0;
  }
}
@media (max-width: 767px) {
  .fenye {
    padding: 15px 0 25px 0;
  }
  .fenye a {
    display: none;
  }
  .fenye .prev,
  .fenye .next {
    display: block;
  }
}
.fenye a,
.fenye span {
  border: 1px solid #DCDCDC;
  background: #fff;
  line-height: 32px;
  color: #333;
  padding: 0 12px;
  margin: 0px 4px;
  font-size: 14px;
}
@media (max-width: 1200px) {
  .fenye a,
  .fenye span {
    font-size: 14px;
    margin: 0 2px;
    line-height: 30px;
    padding: 0 12px;
  }
}
.fenye span {
  border: 0;
}
.fenye a:hover {
  color: #1D6AFF;
}
.fenye .sel {
  background: #1D6AFF;
  color: #fff !important;
  border: 1px solid #1D6AFF;
}
/* 右侧浮框 */
.fu {
  position: fixed;
  top: 50%;
  right: 20px;
  width: 46px;
  z-index: 10;
  background: #fff;
  -moz-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
}
@media (max-width: 767px) {
  .fu {
    display: none;
  }
}
.fu li {
  height: 46px;
  position: relative;
  transition: 0.5s;
  border-radius: 100px;
}
.fu li:hover .spanTell {
  opacity: 1;
}
.fu i,
.fu a {
  display: inline-block;
  width: 46px;
  height: 100%;
  cursor: pointer;
}
.fu .i_wx {
  background: url(../images/f1.png) no-repeat center center;
  background-size: 30px;
}
.fu .i_tell {
  background: url(../images/f2.png) no-repeat center center;
  background-size: 30px;
}
.fu .spanTell {
  opacity: 0;
  line-height: 42px;
  color: #000;
  padding-left: 10px;
  cursor: default;
  font-size: 15px;
}
.fu #go-top i {
  cursor: pointer;
  background: url(../images/f3.png) no-repeat center center;
  background-size: 30px;
}
.fu li:hover .span_wx {
  display: block;
}
.fu .span_wx {
  position: absolute;
  top: 0;
  right: 60px;
  display: none;
  transition: 0.5s;
}
.fu .span_wx img {
  width: 120px;
  height: 120px;
  background: #fff;
  padding: 5px;
}
.fu .li:hover {
  width: 180px;
  margin-left: -140px;
  display: flex;
  background-color: #fff;
}
.fu .li:hover .spanTell {
  opacity: 1;
  width: 80%;
}
/* 底部首页悬浮按钮 */
.ind_btn {
  background-color: #fff;
  height: 70px;
  display: none;
}
@media (max-width: 767px) {
  .ind_btn {
    display: block;
  }
}
.ind_btn p {
  display: flex;
  padding: 10px 15px;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 100;
  background: #fff;
  height: 70px;
  width: 100%;
  align-items: flex-start;
  -moz-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
}
.ind_btn a {
  display: inline-block;
  line-height: 40px;
  text-align: center;
  border: 1px solid #1D6AFF;
  width: 30%;
  font-size: 16px;
  color: #1D6AFF;
  border-radius: 6px;
}
.ind_btn .a2 {
  width: 70%;
  margin-left: 15px;
  color: #fff;
  background: #1D6AFF;
}
/* 试用弹框 */
.tan_shiyong {
  background: url(../images/bg_31.png) repeat-x left top #fff;
  width: 100%;
  max-width: 720px;
  max-height: 95vh;
  overflow-y: auto;
  padding: 40px 40px 20px 40px;
  border-radius: 10px;
  box-sizing: border-box;
}
.tan_shiyong::-webkit-scrollbar {
  width: 6px;
}
.tan_shiyong::-webkit-scrollbar-track-piece {
  width: 6px;
  background-color: #DDDDDD;
}
.tan_shiyong::-webkit-scrollbar-thumb {
  background-color: #1D6AFF;
}
@media (max-width: 767px) {
  .tan_shiyong {
    padding: 15px;
    max-height: 86vh;
  }
}
.tan_shiyong .tip {
  font-size: 12px;
  color: #4E5969;
  line-height: 18px;
  padding-bottom: 20px;
}
.tan_shiyong .top {
  text-align: left;
  font-size: 16px;
  padding-bottom: 15px;
  font-weight: bold;
}
.tan_shiyong .top strong {
  color: #1D6AFF;
}
.tan_shiyong .tb {
  width: 100%;
}
.tan_shiyong .tb .tit {
  font-weight: normal;
  vertical-align: top;
  padding-right: 10px;
  font-size: 14px;
}
.tan_shiyong .tb .tit i {
  font-style: normal;
  color: red;
  padding-right: 5px;
}
.tan_shiyong .tb th,
.tan_shiyong .tb td {
  padding-bottom: 15px;
  line-height: 34px;
}
.tan_shiyong .tb .txt,
.tan_shiyong .tb .select {
  width: 100%;
  height: 34px;
  line-height: 32px;
  border: 1px solid #C9CDD4 !important;
  padding-left: 10px;
  font-size: 14px;
  border-radius: 4px;
  background: #fff;
}
.tan_shiyong .tb .select_city {
  display: flex;
  justify-content: space-between;
}
.tan_shiyong .tb .txt::-webkit-input-placeholder {
  color: #C9CDD4;
}
.tan_shiyong .tb .txt:-moz-placeholder {
  color: #C9CDD4;
}
.tan_shiyong .tb .txt::-moz-placeholder {
  color: #C9CDD4;
}
.tan_shiyong .tb .txt:-ms-input-placeholder {
  color: #C9CDD4;
}
.tan_shiyong .tb .pl20 {
  padding-left: 20px;
}
@media (max-width: 767px) {
  .tan_shiyong .tb .pl20 {
    padding-left: 10px;
  }
}
.tan_shiyong .tb .txt_desc {
  padding: 5px 10px;
  line-height: 26px;
  height: 100px;
}
.tan_shiyong .tb .select {
  width: 48.5%;
  padding-left: 5px;
}
.tan_shiyong .tb .list {
  padding: -5px 0 15px 0;
  display: flex;
  flex-wrap: wrap;
}
.tan_shiyong .tb label {
  width: 33.33%;
  font-size: 14px;
  display: flex;
  cursor: pointer;
  align-items: center;
  line-height: 30px;
}
.tan_shiyong .tb label input {
  width: 16px;
  height: 16px;
  margin-right: 3px;
}
@media (max-width: 767px) {
  .tan_shiyong .tb label {
    width: 100%;
  }
}
.tan_shiyong .tb .list1 {
  justify-content: flex-start;
  flex-wrap: nowrap;
}
.tan_shiyong .tb .list1 label {
  width: auto;
  padding-right: 20px;
}
@media (max-width: 767px) {
  .tan_shiyong .tb .list1 {
    flex-wrap: wrap;
  }
  .tan_shiyong .tb .list1 label {
    padding: 0;
    width: 50%;
  }
}
.tan_shiyong .tb .td_btn {
  text-align: right;
}
.tan_shiyong .tb .btn {
  padding: 0 20px;
  line-height: 32px;
  border: 1px solid #E0E0E6;
  color: #000;
  font-size: 14px;
  cursor: pointer;
  margin-left: 10px;
  border-radius: 3px;
  transition: 0.5s;
}
.tan_shiyong .tb .btn:hover {
  border: 1px solid #1D6AFF;
  color: #1D6AFF;
}
.tan_shiyong .tb .btn2 {
  background: #1D6AFF;
  color: #fff !important;
  border: 1px solid #1D6AFF;
}
.tan_shiyong .tb .btn2:hover {
  background: #0048CE;
}
/* 服务商详情 */
.fwxq_banner {
  background: url(../images/jg_01.png) no-repeat top center;
  background-size: 100% 320px;
  height: 320px;
  display: block;
  width: 100%;
  line-height: 250px;
  text-align: center;
  font-weight: 600;
  font-size: 36px;
  color: #1D2129;
}
.fw_one {
  border-radius: 8px;
  background-color: #fff;
  padding: 16px;
  margin-top: 20px;
  margin-bottom: 10px;
  padding: 25px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: -60px;
}
@media (max-width: 767px) {
  .fw_one {
    position: relative;
    padding: 15px;
    flex-wrap: wrap;
  }
}
.fw_one .left_img {
  width: 100px;
  height: 100px;
  border-radius: 5px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}
.fw_one .left_img img {
  max-width: 100% ;
  max-height: 100%;
}
@media (max-width: 767px) {
  .fw_one .left_img {
    position: relative;
    width: 100%;
    top: 0;
    left: 0;
  }
}
.fw_one .nei {
  width: 100%;
  padding-left: 20px;
}
@media (max-width: 767px) {
  .fw_one .nei {
    padding-left: 0px;
  }
}
.fw_one .title {
  font-weight: bold;
  line-height: 40px;
  font-size: 24px;
  padding-bottom: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media (max-width: 767px) {
  .fw_one .title {
    padding-left: 0;
    font-size: 18px;
    line-height: 26px;
    justify-content: center;
    display: flex;
    flex-wrap: wrap;
  }
  .fw_one .title span {
    width: 100%;
    text-align: center;
  }
}
.fw_one .title .wei {
  padding-left: 25px;
  background: url(../images/bg_01.png) no-repeat left center;
  background-size: 12px;
  padding-left: 22px;
  font-size: 14px;
  font-weight: normal;
}
@media (max-width: 767px) {
  .fw_one .title .wei {
    width: auto;
    margin-top: 5px;
  }
}
.fw_one .txt {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.fw_one .txt dd {
  padding-left: 28px;
  font-size: 14px;
  line-height: 28px;
  color: #515A6E;
}
.fw_one .txt .tit {
  padding-left: 0;
  color: #000;
}
@media (max-width: 767px) {
  .fw_one .txt {
    padding-left: 0;
  }
  .fw_one .txt .wei {
    display: none;
  }
}
.fw_one .desc {
  padding: 5px 0 20px 0;
  font-size: 14px;
  line-height: 24px;
  color: #515A6E;
}
@media (max-width: 767px) {
  .fw_one .desc {
    padding-left: 0;
    padding-top: 10px;
  }
}
.fw_one .a_zixun {
  line-height: 32px;
  display: inline-block;
  border-radius: 4px;
  background: url(../images/bg_25.png) no-repeat 10px center #E8EDF5;
  font-size: 14px;
  padding-left: 33px;
  background-size: 14px;
  color: #1D6AFF;
  padding-right: 15px;
}
.layui-layer-tips .layui-layer-content {
  font-size: 16px !important;
  padding: 5px 10px !important;
}
.fw_two {
  border-radius: 8px;
  margin-bottom: 20px;
  position: relative;
  padding-top: 30px;
  /*
    &::before,&::after{
        content: '';
        position: absolute;
        z-index: 10;
        width: 12px;
        height: 37px;
        top: -23px;
        background: url(../images/bg_24.png) no-repeat left top;
        background-size: 12px 37px;
    }
    &::before{
        left: 10px;
    }
    &::after{
        right: 10px;
    }*/
}
@media (max-width: 1200px) {
  .fw_two {
    padding-top: 30px;
  }
}
@media (max-width: 767px) {
  .fw_two {
    padding-top: 20px;
  }
}
.fw_two .top {
  background-size: 4px 16px;
  padding-left: 15px;
  line-height: 90px;
  text-align: center;
  font-size: 32px;
  margin-left: 15px;
  color: #000;
  font-weight: bold;
  letter-spacing: 3px;
}
@media (max-width: 1200px) {
  .fw_two .top {
    font-size: 28px;
    line-height: 70px;
  }
}
@media (max-width: 767px) {
  .fw_two .top {
    font-size: 22px;
    line-height: 50px;
  }
}
.fw_two .list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}
@media (max-width: 767px) {
  .fw_two .list {
    margin: 0;
    justify-content: space-between;
  }
}
.fw_two .list li {
  padding: 15px;
  width: 33.333%;
}
@media (max-width: 767px) {
  .fw_two .list li {
    width: 48.5%;
    padding: 5px 0;
  }
}
.fw_two .list .nei {
  display: block;
  height: 100%;
  background: #fff;
  border-radius: 8px;
  padding: 10px;
}
@media (max-width: 767px) {
  .fw_two .list .nei {
    padding: 8px;
  }
}
.fw_two .list .pimg {
  width: 100%;
  overflow: hidden;
  height: 180px;
  border-radius: 8px;
}
.fw_two .list .pimg img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
@media (max-width: 767px) {
  .fw_two .list .pimg {
    flex-shrink: 0;
    width: 100%;
    height: 88px;
    border: 1px solid #E5E7EB;
    border-radius: 4px;
  }
}
.fw_two .list .bottom {
  padding: 10px 0;
}
@media (max-width: 767px) {
  .fw_two .list .bottom {
    padding: 0;
  }
}
.fw_two .list .bottom .title {
  font-size: 16px;
  text-align: left;
  font-weight: bold;
  line-height: 30px;
  color: #000;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
@media (max-width: 767px) {
  .fw_two .list .bottom .title {
    text-align: left;
    line-height: 28px;
    color: #000;
    margin-top: 5px;
    font-size: 15px;
  }
}
.fw_two .list .bottom .desc {
  line-height: 22px;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  color: #515a6e;
  padding: 4px 0;
}
@media (max-width: 767px) {
  .fw_two .list .bottom .desc {
    padding: 3px 0;
    -webkit-line-clamp: 2;
  }
}
/* 报名管理 / 营销 */
.bao_banner .container {
  padding: 40px 0;
}
@media (max-width: 767px) {
  .bao_banner .container {
    padding: 10px 15px;
  }
}
.bao_banner .nei .desc {
  font-size: 22px;
  padding-bottom: 40px;
}
@media (max-width: 1200px) {
  .bao_banner .nei .desc {
    padding-bottom: 30px;
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .bao_banner .nei .desc {
    font-size: 14px;
    padding-bottom: 15px;
  }
}
@media (max-width: 1200px) {
  .bao_banner .nei .title {
    width: 130%;
    padding-bottom: 5px;
  }
}
@media (max-width: 767px) {
  .bao_banner .nei .title {
    width: 100%;
  }
}
.bao_banner .nei .tags {
  display: flex;
  align-items: center;
  margin-top: -27px;
  padding-bottom: 35px;
  flex-wrap: wrap;
}
@media (max-width: 1200px) {
  .bao_banner .nei .tags {
    margin-top: -25px;
    padding-bottom: 20px;
    flex-wrap: wrap;
    width: 120%;
  }
}
@media (max-width: 767px) {
  .bao_banner .nei .tags {
    margin-top: -10px;
    padding-bottom: 15px;
    display: none;
  }
}
.bao_banner .nei .tags dd {
  background: #B9D1FF;
  border-radius: 4px;
  line-height: 32px;
  margin-right: 5px;
  margin-bottom: 5px;
  padding: 0 12px;
  font-size: 14px;
}
@media (max-width: 1200px) {
  .bao_banner .nei .tags dd {
    margin-right: 4px;
    margin-bottom: 4px;
  }
}
@media (max-width: 767px) {
  .bao_banner .nei .tags dd {
    line-height: 22px;
    padding: 0 8px;
    font-size: 12px;
  }
}
.bao_one {
  padding: 80px 0 40px 0;
  display: flex;
  margin: 0 -5px;
}
@media (max-width: 767px) {
  .bao_one {
    flex-wrap: wrap;
    padding: 20px 0;
    margin: 0 -5px;
    margin-bottom: -5px;
  }
}
.bao_one li {
  width: 33.333%;
  padding: 0 12px;
}
@media (max-width: 767px) {
  .bao_one li {
    padding: 5px;
  }
}
.bao_one .nei {
  background: #fff;
  display: block;
  border-radius: 10px;
  padding: 30px 0;
  -moz-box-shadow: 0px 15px 32px rgba(0, 0, 0, 0.06);
  -webkit-box-shadow: 0px 15px 32px rgba(0, 0, 0, 0.06);
  box-shadow: 0px 15px 32px rgba(0, 0, 0, 0.06);
  transition: 0.4s;
}
.bao_one .nei:hover {
  margin-top: -20px;
}
@media (max-width: 767px) {
  .bao_one .nei {
    padding: 15px 0 10px 0;
  }
}
.bao_one .pimg {
  padding: 10px 0 15px 0;
  position: relative;
  text-align: center;
}
@media (max-width: 767px) {
  .bao_one .pimg {
    padding: 5px 0;
  }
}
.bao_one .pimg .img {
  height: 80px;
  border-radius: 8px;
}
@media (max-width: 1200px) {
  .bao_one .pimg .img {
    height: 70px;
  }
}
@media (max-width: 767px) {
  .bao_one .pimg .img {
    height: 45px;
  }
}
.bao_one .pimg .zi {
  position: absolute;
  bottom: -15px;
  right: 20px;
  height: 80px;
}
@media (max-width: 1200px) {
  .bao_one .pimg .zi {
    right: 0;
    height: 60px;
    bottom: 0;
  }
}
@media (max-width: 767px) {
  .bao_one .pimg .zi {
    bottom: auto;
    height: 30px;
    top: -15px;
  }
}
.bao_one .title {
  line-height: 40px;
  text-align: center;
  font-weight: bold;
  font-size: 18px;
}
@media (max-width: 767px) {
  .bao_one .title {
    font-size: 14px;
    line-height: 20px;
    padding: 5px 10px 0 10px;
  }
}
.bao_title {
  line-height: 100px;
  text-align: center;
  font-size: 36px;
  color: #1D2129;
  letter-spacing: 2px;
  font-weight: bold;
}
@media (max-width: 1200px) {
  .bao_title {
    font-size: 30px;
    line-height: 80px;
  }
}
@media (max-width: 767px) {
  .bao_title {
    font-size: 21px;
    line-height: 30px;
    margin: 15px 0;
    letter-spacing: 0px;
    white-space: nowrap;
  }
}
.bao_two,
.bao_three,
.bao_four,
.xiao_one,
.xiao_two,
.xiao_three {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 50px;
  /*
    &:hover{
        .pimg img{
            -webkit-transform:scale(1.1);
            -moz-transform:scale(1.1);
            transform:scale(1.1);
        }
    }*/
}
@media (max-width: 1200px) {
  .bao_two,
  .bao_three,
  .bao_four,
  .xiao_one,
  .xiao_two,
  .xiao_three {
    padding: 30px;
    border-radius: 15px;
  }
}
@media (max-width: 767px) {
  .bao_two,
  .bao_three,
  .bao_four,
  .xiao_one,
  .xiao_two,
  .xiao_three {
    flex-wrap: wrap;
    padding: 15px;
    margin-bottom: 30px;
    border-radius: 10px;
  }
}
.bao_two .pimg,
.bao_three .pimg,
.bao_four .pimg,
.xiao_one .pimg,
.xiao_two .pimg,
.xiao_three .pimg {
  width: 46%;
  border-radius: 10px;
  overflow: hidden;
}
.bao_two .pimg img,
.bao_three .pimg img,
.bao_four .pimg img,
.xiao_one .pimg img,
.xiao_two .pimg img,
.xiao_three .pimg img {
  display: block;
  width: 100%;
  transition: 0.5s;
}
@media (max-width: 767px) {
  .bao_two .pimg,
  .bao_three .pimg,
  .bao_four .pimg,
  .xiao_one .pimg,
  .xiao_two .pimg,
  .xiao_three .pimg {
    width: 100%;
    margin-top: 10px;
  }
}
.bao_two .nei,
.bao_three .nei,
.bao_four .nei,
.xiao_one .nei,
.xiao_two .nei,
.xiao_three .nei {
  width: 50%;
}
@media (max-width: 767px) {
  .bao_two .nei,
  .bao_three .nei,
  .bao_four .nei,
  .xiao_one .nei,
  .xiao_two .nei,
  .xiao_three .nei {
    width: 100%;
  }
}
.bao_two {
  background: #E5EAF5;
}
.bao_two li {
  padding: 8px 0;
  display: flex;
  flex-wrap: wrap;
}
@media (max-width: 767px) {
  .bao_two li {
    padding: 5px 0;
  }
}
.bao_two h3 {
  font-size: 20px;
  line-height: 40px;
  color: #1D2129;
  font-weight: bold;
  width: 100%;
}
@media (max-width: 767px) {
  .bao_two h3 {
    font-size: 16px;
    line-height: 40px;
  }
}
.bao_two span {
  padding: 0 15px;
  line-height: 40px;
  background: #FFFFFF;
  border-radius: 8px;
  margin-right: 14px;
  display: flex;
  align-items: center;
  font-size: 16px;
}
.bao_two span img {
  width: 20px;
  margin-right: 8px;
}
@media (max-width: 767px) {
  .bao_two span img {
    width: 16px;
    margin-right: 5px;
  }
}
@media (max-width: 1200px) {
  .bao_two span {
    margin-bottom: 5px;
    margin-right: 10px;
    padding: 0 10px;
    font-size: 14px;
    line-height: 36px;
  }
}
@media (max-width: 767px) {
  .bao_two span {
    padding: 0 10px;
    white-space: nowrap;
  }
}
.bao_two span:last-child {
  margin-right: 0;
}
.bao_three {
  background: #FFF0E0;
}
.bao_three li {
  padding: 13px 0;
  line-height: 26px;
  display: flex;
  align-items: center;
  color: #1D2129;
}
@media (max-width: 767px) {
  .bao_three li {
    padding: 8px 0;
    line-height: 22px;
  }
}
.bao_three .tit {
  padding-right: 32px;
  margin-right: 12px;
  background: url(../images/bg_28.png) no-repeat right center;
  background-size: 20px;
  font-size: 18px;
  font-weight: bold;
  white-space: nowrap;
}
@media (max-width: 1200px) {
  .bao_three .tit {
    font-size: 16px;
  }
}
@media (max-width: 767px) {
  .bao_three .tit {
    font-size: 15px;
    background-size: 18px;
    padding-right: 28px;
  }
}
.bao_three .desc {
  font-size: 18px;
}
@media (max-width: 1200px) {
  .bao_three .desc {
    font-size: 16px;
  }
}
@media (max-width: 767px) {
  .bao_three .desc {
    font-size: 15px;
  }
}
.bao_four {
  background: #E5F1F5;
}
.bao_four li {
  display: inline-block;
  line-height: 60px;
  margin: 15px 0;
  padding: 0 30px 0 60px;
  border-radius: 50px;
  font-size: 17px;
  margin-right: 20px;
  background: url(../images/bg_29.png) no-repeat 20px center #fff;
  background-size: 24px;
}
@media (max-width: 1200px) {
  .bao_four li {
    margin: 10px 0;
    line-height: 46px;
    padding: 0 20px 0 40px;
    background: url(../images/bg_29.png) no-repeat 14px center #fff;
    background-size: 18px;
    font-size: 16px;
  }
}
@media (max-width: 767px) {
  .bao_four li {
    line-height: 42px;
    font-size: 15px;
  }
}
.bao_five {
  overflow: hidden;
  border-radius: 20px;
  margin-bottom: 50px;
}
.bao_five img {
  display: block;
  width: 100%;
}
@media (max-width: 1200px) {
  .bao_five {
    border-radius: 15px;
  }
}
@media (max-width: 767px) {
  .bao_five {
    margin-bottom: 30px;
    border-radius: 10px;
  }
}
.anquan {
  background: #E1E6F1;
  padding: 30px 0 25px 0;
  border-radius: 20px;
  display: flex;
  margin-bottom: 80px;
}
@media (max-width: 1200px) {
  .anquan {
    padding: 25px 0 20px 0;
    border-radius: 15px;
    margin-bottom: 50px;
  }
}
@media (max-width: 767px) {
  .anquan {
    border-radius: 10px;
    padding: 20px 0 15px 0;
    margin-bottom: 30px;
  }
}
.anquan li {
  width: 33.333%;
  text-align: center;
  transition: 0.5s;
}
.anquan .pimg {
  padding-bottom: 10px;
}
.anquan .pimg img {
  height: 120px;
}
@media (max-width: 1200px) {
  .anquan .pimg img {
    height: 100px;
  }
}
@media (max-width: 767px) {
  .anquan .pimg img {
    height: 60px;
  }
}
.anquan .title {
  font-size: 20px;
  color: #1D2129;
  line-height: 38px;
  font-weight: bold;
}
@media (max-width: 1200px) {
  .anquan .title {
    font-size: 18px;
  }
}
@media (max-width: 767px) {
  .anquan .title {
    font-size: 15px;
    line-height: 24px;
  }
}
.anquan .desc {
  font-size: 16px;
  color: #1D6AFF;
  line-height: 24px;
}
@media (max-width: 1200px) {
  .anquan .desc {
    font-size: 14px;
  }
}
@media (max-width: 767px) {
  .anquan .desc {
    font-size: 12px;
    line-height: 20px;
  }
}
.xiao_one,
.xiao_two,
.xiao_three {
  background: #E5F1F5;
}
@media (max-width: 767px) {
  .xiao_one ul,
  .xiao_two ul,
  .xiao_three ul {
    margin-top: -15px;
  }
}
.xiao_one li,
.xiao_two li,
.xiao_three li {
  padding-left: 35px;
  background: url(../images/bg_29.png) no-repeat left 2px;
  background-size: 24px;
  margin: 28px 0;
  line-height: 28px;
  font-size: 19px;
  letter-spacing: 1px;
}
@media (max-width: 1200px) {
  .xiao_one li,
  .xiao_two li,
  .xiao_three li {
    padding-left: 30px;
    background: url(../images/bg_29.png) no-repeat left 2px;
    background-size: 22px;
    line-height: 24px;
    font-size: 17px;
    margin: 20px 0;
  }
}
@media (max-width: 767px) {
  .xiao_one li,
  .xiao_two li,
  .xiao_three li {
    padding-left: 30px;
    background: url(../images/bg_29.png) no-repeat left 4px;
    background-size: 20px;
    line-height: 24px;
    font-size: 15px;
    margin: 15px 0;
  }
}
.xiao_two {
  background: #E5E5F5;
}
.xiao_two li {
  background: url(../images/bg_29-2.png) no-repeat left 2px;
  background-size: 24px;
}
@media (max-width: 1200px) {
  .xiao_two li {
    background: url(../images/bg_29-2.png) no-repeat left 2px;
    background-size: 22px;
  }
}
@media (max-width: 767px) {
  .xiao_two li {
    padding-left: 30px;
    background: url(../images/bg_29-2.png) no-repeat left 4px;
    background-size: 20px;
  }
}
.xiao_three {
  background: #FFF0E0;
}
.xiao_three li {
  background: url(../images/bg_28.png) no-repeat left 4px;
  background-size: 20px;
}
@media (max-width: 1200px) {
  .xiao_three li {
    background: url(../images/bg_28.png) no-repeat left 4px;
    background-size: 20px;
  }
}
@media (max-width: 767px) {
  .xiao_three li {
    padding-left: 30px;
    background: url(../images/bg_28.png) no-repeat left 4px;
    background-size: 20px;
  }
}
/* 会展 */
.hz_one {
  margin: 0 -10px;
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 40px;
}
@media (max-width: 1200px) {
  .hz_one {
    padding-bottom: 30px;
  }
}
@media (max-width: 767px) {
  .hz_one {
    margin: 0;
    padding-bottom: 10px;
  }
}
.hz_one li {
  width: 33.333%;
  padding: 10px;
}
@media (max-width: 767px) {
  .hz_one li {
    width: 100%;
    padding: 0 0 15px 0;
  }
}
.hz_one .nei {
  background: #fff;
  border-radius: 12px;
  padding: 40px 20px;
  display: block;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: 0.5s;
}
@media (max-width: 1200px) {
  .hz_one .nei {
    padding: 30px 15px;
  }
}
.hz_one .nei:hover {
  margin-top: -15px;
  -moz-box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.08);
  -webkit-box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.08);
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.08);
}
.hz_one .pimg {
  padding-bottom: 8px;
}
.hz_one .pimg img {
  height: 80px;
  border-radius: 8px;
}
.hz_one .title {
  line-height: 50px;
  font-weight: bold;
  color: #1D2129;
  font-size: 18px;
  letter-spacing: 1px;
}
.hz_one .desc {
  font-size: 16px;
  color: #1D2129;
  line-height: 26px;
}
@media (max-width: 1200px) {
  .hz_one .desc {
    font-size: 14px;
    line-height: 24px;
  }
}
@media (max-width: 767px) {
  .hz_two li {
    width: 33.33%;
  }
}
.hz_list li {
  width: 25%;
}
@media (max-width: 1200px) {
  .hz_list li {
    width: 33.33%;
  }
}
@media (max-width: 767px) {
  .hz_list li {
    width: 50%;
  }
}
.hz_more {
  line-height: 50px;
  text-align: center;
}
.hz_more a {
  color: #000;
}
.hz_more a:hover {
  color: #1D6AFF;
}
/* 展馆 */
.zg_yaosu {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.zg_yaosu li {
  width: 49.1%;
  margin-bottom: 24px;
  padding: 25px;
  background: #fff;
  border-radius: 12px;
  transition: 0.5s;
}
@media (max-width: 767px) {
  .zg_yaosu li {
    width: 100%;
    margin-bottom: 20px;
    padding: 15px;
  }
  .zg_yaosu li:last-child {
    margin-bottom: 10px;
  }
}
.zg_yaosu li .bottom {
  background: #FFEEE3;
  border-radius: 8px;
  font-size: 15px;
  color: #1D2129;
  text-align: center;
  line-height: 48px;
}
.zg_yaosu li:nth-child(2) .bottom {
  background: #DEECFF;
}
.zg_yaosu li:nth-child(3) .bottom {
  background: #FFF3D3;
}
.zg_yaosu li:nth-child(4) .bottom {
  background: #D9FFDE;
}
.zg_yaosu li:hover {
  -moz-box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.2);
}
.zg_yaosu .title {
  padding-bottom: 25px;
  line-height: 26px;
  font-size: 19px;
  color: #1D2129;
  font-weight: bold;
  width: 100%;
  text-align: center;
  letter-spacing: 1px;
}
@media (max-width: 767px) {
  .zg_yaosu .title {
    font-size: 18px;
    padding-bottom: 15px;
  }
}
.zg_yaosu .info {
  display: flex;
  justify-content: space-between;
  margin: 0 -7px;
  padding-bottom: 20px;
}
.zg_yaosu .info dl {
  width: 50%;
  margin: 0 7px;
  background: #F7F9FC;
  border-radius: 8px;
  padding: 15px 20px 20px 20px;
  text-align: center;
}
@media (max-width: 1200px) {
  .zg_yaosu .info dl {
    padding: 10px 15px 15px 15px;
  }
}
@media (max-width: 767px) {
  .zg_yaosu .info dl {
    padding: 5px 10px 10px 10px;
  }
}
.zg_yaosu .info .tit {
  line-height: 24px;
  padding: 8px 0;
  color: #1D2129;
  font-size: 16px;
  font-weight: bold;
}
@media (max-width: 767px) {
  .zg_yaosu .info .tit {
    font-size: 15px;
  }
}
.zg_yaosu .info .desc {
  height: 72px;
  color: #4E5969;
  line-height: 24px;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
@media (max-width: 767px) {
  .zg_yaosu .info .desc {
    font-size: 13px;
    line-height: 23px;
    -webkit-line-clamp: 7;
    height: auto;
    text-align: justify;
  }
}
/* 新会展crm  */
.crm1 {
  text-align: center;
  padding-bottom: 50px;
  margin-top: -10px;
}
@media (max-width: 1200px) {
  .crm1 {
    padding-bottom: 40px;
  }
}
@media (max-width: 767px) {
  .crm1 {
    padding-bottom: 30px;
  }
}
.crm1 .title {
  font-size: 24px;
  color: #1D2129;
  line-height: 40px;
  padding-bottom: 10px;
  font-weight: bold;
}
@media (max-width: 1200px) {
  .crm1 .title {
    font-size: 20px;
    line-height: 30px;
  }
}
@media (max-width: 767px) {
  .crm1 .title {
    font-size: 16px;
    line-height: 24px;
  }
}
.crm1 .desc {
  padding: 0 30px;
  font-size: 16px;
  color: #1D2129;
  line-height: 22px;
}
@media (max-width: 767px) {
  .crm1 .desc {
    padding: 0;
    font-size: 14px;
    text-align: justify;
  }
}
.crm {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 50px;
  background: #E5EAF5;
}
@media (max-width: 1200px) {
  .crm {
    padding: 30px;
    border-radius: 15px;
  }
}
@media (max-width: 767px) {
  .crm {
    flex-wrap: wrap;
    padding: 15px;
    margin-bottom: 30px;
    border-radius: 10px;
  }
}
.crm .pimg {
  width: 46%;
  border-radius: 10px;
  overflow: hidden;
}
.crm .pimg img {
  display: block;
  width: 100%;
  transition: 0.5s;
}
@media (max-width: 767px) {
  .crm .pimg {
    width: 100%;
    margin-top: 10px;
  }
}
.crm .nei {
  width: 50%;
}
@media (max-width: 767px) {
  .crm .nei {
    width: 100%;
  }
}
.crm .nei .title {
  line-height: 30px;
  font-size: 22px;
  font-weight: bold;
  color: #1D2129;
  padding-bottom: 20px;
}
@media (max-width: 1200px) {
  .crm .nei .title {
    font-size: 20px;
    padding-bottom: 15px;
  }
}
@media (max-width: 767px) {
  .crm .nei .title {
    font-size: 18px;
    padding-bottom: 14px;
  }
}
.crm .nei li {
  padding-left: 35px;
  background: url(../images/bg_29-3.png) no-repeat left 3px;
  color: #1D2129;
  font-size: 18px;
  line-height: 28px;
  padding-bottom: 20px;
}
@media (max-width: 1200px) {
  .crm .nei li {
    font-size: 16px;
    line-height: 24px;
    background-size: 18px;
    padding-left: 30px;
    padding-bottom: 15px;
  }
}
@media (max-width: 767px) {
  .crm .nei li {
    font-size: 15px;
    background-size: 16px;
    padding-left: 26px;
  }
}
.crm .nei .info {
  padding-left: 35px;
  padding-top: 10px;
}
@media (max-width: 1200px) {
  .crm .nei .info {
    padding-left: 30px;
  }
}
@media (max-width: 767px) {
  .crm .nei .info {
    padding-left: 26px;
    padding-bottom: 10px;
    padding-top: 0;
  }
}
.crm .nei .info .num {
  display: block;
  width: 100%;
  font-size: 48px;
  color: #1D2129;
  line-height: 56px;
  font-weight: bold;
}
@media (max-width: 1200px) {
  .crm .nei .info .num {
    font-size: 38px;
    line-height: 50px;
  }
}
@media (max-width: 767px) {
  .crm .nei .info .num {
    font-size: 34px;
    line-height: 40px;
  }
}
.crm .nei .info dd {
  color: #1D2129;
  font-size: 18px;
  line-height: 28px;
}
@media (max-width: 1200px) {
  .crm .nei .info dd {
    font-size: 16px;
    line-height: 24px;
  }
}
@media (max-width: 767px) {
  .crm .nei .info dd {
    font-size: 15px;
  }
}
.crm .nei .img {
  display: block;
  max-width: 100%;
}
.crm2 {
  background: #E5F1F5;
}
.crm2 .nei li {
  background: url(../images/bg_29-4.png) no-repeat left 3px;
}
@media (max-width: 1200px) {
  .crm2 .nei li {
    background-size: 18px;
  }
}
@media (max-width: 767px) {
  .crm2 .nei li {
    background-size: 16px;
  }
}
.crm3 {
  background: #FFF0E0;
  flex-wrap: wrap;
}
.crm3 .nei li {
  background: url(../images/bg_29-5.png) no-repeat left 3px;
}
@media (max-width: 1200px) {
  .crm3 .nei li {
    background-size: 18px;
  }
}
@media (max-width: 767px) {
  .crm3 .nei li {
    background-size: 16px;
  }
}
.crm3 .list {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
@media (max-width: 767px) {
  .crm3 .list {
    flex-wrap: wrap;
  }
}
.crm3 .list li {
  width: 50%;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  padding: 20px;
  margin: 0 8px;
  margin-top: 35px;
}
@media (max-width: 1200px) {
  .crm3 .list li {
    padding: 15px;
    width: 100%;
    margin-top: 15px;
  }
}
.crm3 .list li:last-child {
  margin-right: 0;
}
.crm3 .list li:first-child {
  margin-left: 0;
}
@media (max-width: 767px) {
  .crm3 .list li {
    margin: 0;
    margin-top: 15px;
  }
}
.crm3 .list .tit {
  font-size: 16px;
  color: #1D2129;
  line-height: 24px;
  padding-bottom: 8px;
  font-weight: bold;
}
.crm3 .list .desc {
  font-size: 13px;
  color: #4E5969;
  line-height: 22px;
  text-align: justify;
}
.crm4 {
  background: #E5E5F5;
}
.crm4 .nei li {
  background: url(../images/bg_29-2.png) no-repeat left 3px;
  background-size: 24px;
}
@media (max-width: 1200px) {
  .crm4 .nei li {
    background-size: 18px;
  }
}
@media (max-width: 767px) {
  .crm4 .nei li {
    background-size: 16px;
  }
}
.crm5 {
  flex-wrap: wrap;
}
.crm5 .top {
  width: 100%;
  display: block;
  text-align: center;
  padding-bottom: 40px;
}
@media (max-width: 1200px) {
  .crm5 .top {
    padding-bottom: 30px;
  }
}
@media (max-width: 767px) {
  .crm5 .top {
    padding-bottom: 20px;
  }
}
.crm5 .top .tit {
  font-size: 26px;
  font-weight: bold;
  line-height: 40px;
  padding-bottom: 10px;
}
@media (max-width: 1200px) {
  .crm5 .top .tit {
    font-size: 22px;
  }
}
@media (max-width: 767px) {
  .crm5 .top .tit {
    font-size: 18px;
    padding-bottom: 5px;
  }
}
.crm5 .top .desc {
  font-size: 20px;
  color: #1D2129;
  line-height: 28px;
}
@media (max-width: 1200px) {
  .crm5 .top .desc {
    font-size: 18px;
  }
}
@media (max-width: 767px) {
  .crm5 .top .desc {
    line-height: 24px;
    font-size: 15px;
  }
}
.crm6 {
  background: #E0EAFF;
}
.crm6 .nei {
  display: flex;
  justify-content: space-between;
  padding-top: 30px;
}
@media (max-width: 1200px) {
  .crm6 .nei {
    padding-top: 0px;
  }
}
.crm6 .nei .info {
  width: 50%;
}
/* 新数智现场 */
.mb0 {
  margin-bottom: 0 !important;
}
.sz_title {
  text-align: center;
  padding: 70px 0 20px 0;
}
@media (max-width: 1200px) {
  .sz_title {
    padding: 50px 0 15px 0;
  }
}
@media (max-width: 767px) {
  .sz_title {
    padding: 40px 0 15px 0;
  }
}
.sz_title h2 {
  font-size: 36px;
  color: #1D2129;
  letter-spacing: 2px;
  font-weight: bold;
  line-height: 40px;
}
@media (max-width: 1200px) {
  .sz_title h2 {
    font-size: 30px;
    line-height: 30px;
  }
}
@media (max-width: 767px) {
  .sz_title h2 {
    font-size: 21px;
    line-height: 20px;
  }
}
.sz_title p {
  font-size: 18px;
  color: #1D2129;
  line-height: 30px;
  font-weight: normal;
  padding-top: 15px;
}
@media (max-width: 767px) {
  .sz_title p {
    font-size: 14px;
    line-height: 24px;
    padding-top: 9px;
  }
}
.sz_one {
  text-align: center;
  font-weight: bold;
  font-size: 23px;
  color: #1D2129;
  line-height: 40px;
  letter-spacing: 1px;
  padding-bottom: 15px;
}
@media (max-width: 1200px) {
  .sz_one {
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .sz_one {
    font-size: 16px;
    padding-bottom: 6px;
  }
}
.sz_two {
  display: flex;
  align-items: center;
  padding-bottom: 40px;
}
@media (max-width: 1200px) {
  .sz_two {
    padding-bottom: 30px;
  }
}
@media (max-width: 767px) {
  .sz_two {
    padding-bottom: 20px;
  }
}
.sz_two li {
  width: 50%;
  background: #FFFFFF;
  box-shadow: 0px 20px 30px 0px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  text-align: center;
  padding: 20px 0;
}
@media (max-width: 1200px) {
  .sz_two li {
    padding: 15px 0;
  }
}
@media (max-width: 767px) {
  .sz_two li {
    border-radius: 8px;
    padding: 10px 0;
  }
}
.sz_two li .img {
  height: 120px;
  border-radius: 8px;
}
@media (max-width: 1200px) {
  .sz_two li .img {
    height: 90px;
  }
}
@media (max-width: 767px) {
  .sz_two li .img {
    height: 50px;
  }
}
.sz_two li h3 {
  font-size: 18px;
  font-weight: bold;
  line-height: 32px;
  padding-top: 8px;
}
@media (max-width: 1200px) {
  .sz_two li h3 {
    font-size: 16px;
  }
}
@media (max-width: 767px) {
  .sz_two li h3 {
    font-size: 14px;
    line-height: 20px;
    padding-top: 12px;
  }
}
.sz_two .jiantou {
  background: url(../images/bg_30.png) no-repeat center center;
  background-size: 24px;
  width: 54px;
  flex-shrink: 0;
  box-shadow: none;
}
@media (max-width: 1200px) {
  .sz_two .jiantou {
    width: 42px;
    background-size: 20px;
  }
}
@media (max-width: 767px) {
  .sz_two .jiantou {
    width: 22px;
    background-size: 14px;
  }
}
.sz_three {
  background: #FFFFFF;
  box-shadow: 0px 20px 30px 0px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  display: flex;
  padding: 20px 0;
  margin-bottom: 40px;
}
@media (max-width: 767px) {
  .sz_three {
    flex-wrap: wrap;
    padding-bottom: 0;
    margin-bottom: 20px;
  }
}
.sz_three li {
  width: 50%;
  text-align: center;
}
.sz_three li .img {
  height: 40px;
  border-radius: 8px;
}
.sz_three li p {
  font-size: 14px;
  color: #1D2129;
  line-height: 22px;
  padding-top: 8px;
}
@media (max-width: 1200px) {
  .sz_three li p {
    font-size: 13px;
  }
}
@media (max-width: 767px) {
  .sz_three li p {
    font-size: 12px;
    white-space: nowrap;
  }
}
@media (max-width: 767px) {
  .sz_three li {
    width: 18%;
    margin-bottom: 15px;
  }
}
.sz_three .jiantou {
  width: 24px;
  background: url(../images/bg_30.png) no-repeat center center;
  background-size: 24px;
  flex-shrink: 0;
}
@media (max-width: 1200px) {
  .sz_three .jiantou {
    background-size: 20px;
  }
}
@media (max-width: 767px) {
  .sz_three .jiantou {
    background-size: 14px;
  }
}
.sz_four {
  display: flex;
  margin: 0 -10px;
  flex-wrap: wrap;
  margin-top: -10px;
}
@media (max-width: 767px) {
  .sz_four {
    margin: 0;
    justify-content: space-between;
    margin-bottom: -10px;
  }
}
.sz_four li {
  width: 33.333%;
  padding: 10px;
}
.sz_four li:hover .pimg img {
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  transform: scale(1.1);
}
.sz_four li:hover .nei {
  -moz-box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.08);
  -webkit-box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.08);
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.08);
}
@media (max-width: 767px) {
  .sz_four li {
    padding: 0;
    width: 48.5%;
    margin-bottom: 15px;
  }
}
.sz_four .nei {
  background: #fff;
  border-radius: 8px;
  padding: 12px;
  display: block;
  width: 100%;
  height: 100%;
  transition: 0.4s;
}
@media (max-width: 767px) {
  .sz_four .nei {
    padding: 8px;
  }
}
.sz_four .pimg {
  border-radius: 8px;
  overflow: hidden;
}
.sz_four .pimg img {
  display: block;
  width: 100%;
}
.sz_four .title {
  padding-top: 5px;
  text-align: center;
  line-height: 50px;
  font-size: 22px;
  color: #1D2129;
  font-weight: bold;
  letter-spacing: 1px;
}
@media (max-width: 1200px) {
  .sz_four .title {
    line-height: 40px;
    font-size: 18px;
  }
}
@media (max-width: 767px) {
  .sz_four .title {
    line-height: 40px;
    font-size: 16px;
  }
}
.sz_four .desc {
  font-size: 15px;
  color: #4E5969;
  line-height: 24px;
  text-align: center;
  padding-bottom: 5px;
}
@media (max-width: 1200px) {
  .sz_four .desc {
    font-size: 14px;
    line-height: 22px;
  }
}
.sz_five {
  background: #E1E6F1;
  border-radius: 20px;
  padding: 35px 95px;
  margin-top: 10px;
}
@media (max-width: 1200px) {
  .sz_five {
    padding: 35px 55px;
  }
}
@media (max-width: 767px) {
  .sz_five {
    padding: 15px;
    margin-top: 5px;
  }
}
.sz_five .img {
  display: block;
  max-width: 100%;
}
.sz_five .title {
  text-align: center;
  padding-bottom: 10px;
  font-weight: bold;
  font-size: 22px;
  color: #1D2129;
  line-height: 30px;
}
@media (max-width: 1200px) {
  .sz_five .title {
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .sz_five .title {
    font-size: 16px;
    line-height: 26px;
  }
}
.sz_five .bottom {
  padding-top: 30px;
  text-align: center;
}
@media (max-width: 767px) {
  .sz_five .bottom {
    padding-bottom: 15px;
  }
}
.sz_five .bottom p {
  font-size: 22px;
  color: #1D2129;
  line-height: 30px;
  font-weight: bold;
  padding-bottom: 15px;
}
@media (max-width: 1200px) {
  .sz_five .bottom p {
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .sz_five .bottom p {
    font-size: 16px;
    line-height: 26px;
    padding-bottom: 10px;
  }
}
.sz_five .bottom .btn {
  display: inline-block;
  width: 200px;
  height: 48px;
  background: #1D6AFF;
  border-radius: 8px;
  color: #fff;
  line-height: 48px;
  font-size: 16px;
}
.sz_five .bottom .btn:hover {
  background: #0051E8;
}
@media (max-width: 767px) {
  .sz_five .bottom .btn {
    width: 150px;
    height: 42px;
    line-height: 42px;
  }
}
.sz_six {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: -20px;
}
@media (max-width: 1200px) {
  .sz_six {
    margin-bottom: -15px;
  }
}
@media (max-width: 767px) {
  .sz_six {
    margin-bottom: -15px;
  }
}
.sz_six li {
  width: 49%;
  margin-bottom: 20px;
  background: #fff;
  border-radius: 20px;
  padding: 25px;
  display: flex;
  transition: 0.4s;
}
@media (max-width: 1200px) {
  .sz_six li {
    padding: 15px;
    border-radius: 15px;
  }
}
@media (max-width: 767px) {
  .sz_six li {
    flex-wrap: wrap;
    width: 48.5%;
    margin-bottom: 15px;
    border-radius: 8px;
    padding: 8px;
    justify-content: flex-start;
  }
}
.sz_six li:hover {
  -moz-box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.08);
  -webkit-box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.08);
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.08);
}
.sz_six li:hover .pimg img {
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  transform: scale(1.1);
}
.sz_six .pimg {
  width: 240px;
  height: 160px;
  border-radius: 12px;
  overflow: hidden;
  flex-shrink: 0;
}
.sz_six .pimg img {
  display: block;
  width: 100%;
  height: 100%;
}
@media (max-width: 1200px) {
  .sz_six .pimg {
    width: 160px;
    height: 100px;
  }
}
@media (max-width: 767px) {
  .sz_six .pimg {
    width: 100%;
    height: auto;
    border-radius: 6px;
  }
  .sz_six .pimg img {
    height: auto;
  }
}
.sz_six .txt {
  width: 100%;
  padding-left: 25px;
}
@media (max-width: 1200px) {
  .sz_six .txt {
    padding-left: 20px;
  }
}
@media (max-width: 767px) {
  .sz_six .txt {
    padding-left: 0;
    padding-top: 10px;
    height: 100%;
  }
}
.sz_six .txt .title {
  padding-bottom: 15px;
  font-size: 20px;
  color: #1D2129;
  line-height: 24px;
  font-weight: bold;
}
@media (max-width: 1200px) {
  .sz_six .txt .title {
    font-size: 18px;
    padding-bottom: 10px;
  }
}
@media (max-width: 767px) {
  .sz_six .txt .title {
    font-size: 16px;
  }
}
.sz_six .txt .desc {
  font-size: 16px;
  color: #4E5969;
  line-height: 24px;
}
@media (max-width: 1200px) {
  .sz_six .txt .desc {
    font-size: 14px;
    line-height: 22px;
  }
}
.sz_seven {
  padding: 12px;
  background: #fff;
  border-radius: 20px;
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}
@media (max-width: 1200px) {
  .sz_seven {
    padding: 8px;
    border-radius: 15px;
  }
}
@media (max-width: 767px) {
  .sz_seven {
    padding: 5px;
    border-radius: 6px;
    margin-top: 0;
  }
}
.sz_seven li {
  width: 33.33%;
  padding: 12px;
}
@media (max-width: 1200px) {
  .sz_seven li {
    padding: 8px;
  }
}
@media (max-width: 767px) {
  .sz_seven li {
    padding: 5px;
  }
}
.sz_seven li:hover img {
  -webkit-transform: scale(1.05);
  -moz-transform: scale(1.05);
  transform: scale(1.05);
}
.sz_seven img {
  display: block;
  width: 100%;
  border-radius: 12px;
}
@media (max-width: 767px) {
  .sz_seven img {
    border-radius: 5px;
  }
}
.sz_eight {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -12px;
  padding-bottom: 40px;
}
@media (max-width: 767px) {
  .sz_eight {
    margin: 0;
    justify-content: space-between;
    padding-bottom: 30px;
  }
}
.sz_eight li {
  width: 25%;
  padding: 12px;
}
@media (max-width: 1200px) {
  .sz_eight li {
    width: 33.333%;
  }
}
@media (max-width: 767px) {
  .sz_eight li {
    width: 48.1%;
    padding: 0;
    padding-bottom: 15px;
  }
}
.sz_eight .nei {
  background: #fff;
  display: block;
  width: 100%;
  padding: 12px;
  border-radius: 12px;
}
@media (max-width: 767px) {
  .sz_eight .nei {
    border-radius: 8px;
  }
}
.sz_eight .pimg {
  width: 100%;
  display: block;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
}
@media (max-width: 1200px) {
  .sz_eight .pimg {
    height: 160px;
  }
}
@media (max-width: 767px) {
  .sz_eight .pimg {
    height: 120px;
    border-radius: 6px;
  }
}
.sz_eight .pimg img {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}
.sz_eight h3 {
  text-align: center;
  font-size: 15px;
  line-height: 26px;
  color: #1D2129;
  padding-top: 8px;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
@media (max-width: 767px) {
  .sz_eight h3 {
    font-size: 14px;
    line-height: 22px;
    -webkit-line-clamp: 2;
  }
}
.body_hidden {
  overflow: hidden;
  height: 100vh;
}
/* 参展商 */
.czs_one {
  padding: 80px 0 0 0;
  display: flex;
  flex-wrap: wrap;
  margin: 0 -12px;
}
@media (max-width: 1200px) {
  .czs_one {
    padding: 50px 0 0 0;
    margin: 0 -10px;
  }
}
@media (max-width: 767px) {
  .czs_one {
    padding: 20px 0 0 0;
    margin: 0;
  }
}
.czs_one li {
  width: 33.333%;
  padding: 12px;
}
@media (max-width: 1200px) {
  .czs_one li {
    padding: 10px;
  }
}
@media (max-width: 767px) {
  .czs_one li {
    padding: 0px;
    width: 100%;
    margin-bottom: 15pxs;
  }
}
.czs_one .nei {
  background: #fff;
  padding: 25px;
  border-radius: 12px;
  color: #1D2129;
  transition: 0.5s;
}
@media (max-width: 1200px) {
  .czs_one .nei {
    padding: 15px;
  }
}
.czs_one .nei:hover {
  -moz-box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.2);
}
.czs_one .title {
  font-size: 18px;
  font-weight: bold;
  line-height: 28px;
}
@media (max-width: 1200px) {
  .czs_one .title {
    font-size: 17px;
  }
}
.czs_one .desc {
  padding: 12px 0 24px 0;
  font-size: 16px;
  line-height: 26px;
}
@media (max-width: 1200px) {
  .czs_one .desc {
    padding: 10px 0 15px 0;
    font-size: 15px;
    line-height: 24px;
  }
}
.czs_one .img {
  display: block;
  width: 100%;
  border-radius: 8px;
}
.czs_two {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding-bottom: 20px;
}
@media (max-width: 767px) {
  .czs_two {
    padding-bottom: 10px;
  }
}
.czs_two li {
  width: 49%;
  background: #fff;
  border-radius: 12px;
  margin-bottom: 24px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  transition: 0.5s;
}
@media (max-width: 767px) {
  .czs_two li {
    width: 100%;
    margin-bottom: 15px;
    padding: 15px;
  }
}
.czs_two li:hover {
  -moz-box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.2);
}
.czs_two .img {
  flex-shrink: 0;
  width: 60px;
}
.czs_two .nei {
  width: 100%;
  padding-left: 20px;
}
@media (max-width: 767px) {
  .czs_two .nei {
    padding-left: 15px;
  }
}
.czs_two .nei .tit {
  line-height: 30px;
  font-size: 18px;
  color: #1D2129;
  font-weight: bold;
}
@media (max-width: 767px) {
  .czs_two .nei .tit {
    font-size: 17px;
  }
}
.czs_two .nei .desc {
  font-size: 16px;
  color: #1D2129;
  line-height: 24px;
  padding-top: 3px;
}
@media (max-width: 767px) {
  .czs_two .nei .desc {
    font-size: 14px;
    line-height: 22px;
  }
}
.czs_three {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding-bottom: 50px;
}
.czs_three li {
  width: 49.1%;
  margin-bottom: 24px;
  padding: 25px;
  background: #fff;
  border-radius: 12px;
  transition: 0.5s;
}
@media (max-width: 767px) {
  .czs_three li {
    width: 100%;
    margin-bottom: 20px;
    padding: 15px;
  }
  .czs_three li:last-child {
    margin-bottom: 10px;
  }
}
.czs_three li:hover {
  -moz-box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.2);
}
.czs_three .title {
  padding-bottom: 25px;
  line-height: 26px;
  font-size: 22px;
  color: #1D2129;
  font-weight: bold;
  width: 100%;
  text-align: center;
  letter-spacing: 1px;
}
@media (max-width: 767px) {
  .czs_three .title {
    font-size: 18px;
    padding-bottom: 15px;
  }
}
.czs_three .img {
  display: block;
  width: 100%;
  border-radius: 10px;
}
.czs_three .info {
  display: flex;
  justify-content: space-between;
  margin: 0 -7px;
  padding-bottom: 20px;
}
.czs_three .info dl {
  width: 50%;
  margin: 0 7px;
  background: #F7F9FC;
  border-radius: 8px;
  padding: 15px 18px;
}
@media (max-width: 1200px) {
  .czs_three .info dl {
    padding: 10px 15px;
  }
}
@media (max-width: 767px) {
  .czs_three .info dl {
    padding: 5px 10px 10px 10px;
  }
}
.czs_three .info .tit {
  line-height: 24px;
  padding-bottom: 8px;
  color: #1D2129;
  font-size: 16px;
  font-weight: bold;
}
@media (max-width: 767px) {
  .czs_three .info .tit {
    font-size: 15px;
  }
}
.czs_three .info .desc {
  color: #4E5969;
  line-height: 22px;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 8;
  -webkit-box-orient: vertical;
}
@media (max-width: 767px) {
  .czs_three .info .desc {
    font-size: 13px;
    line-height: 23px;
    -webkit-line-clamp: 7;
    height: auto;
    text-align: justify;
  }
}
/* 动态展位图 */
.zwt1 {
  background: #E5EAF5;
}
.zwt1 .tit {
  color: #1D2129;
  font-size: 19px;
  font-weight: bold;
}
@media (max-width: 767px) {
  .zwt1 .tit {
    font-size: 16px;
  }
}
.zwt1 .desc {
  padding: 6px 0;
  font-size: 16px;
  color: #1D2129;
  line-height: 26px;
}
@media (max-width: 767px) {
  .zwt1 .desc {
    font-size: 14px;
    line-height: 22px;
  }
}
.zwt1 .tag {
  display: flex;
}
.zwt1 .tag dd {
  background: #fff;
  font-size: 14px;
  color: #1D2129;
  line-height: 22px;
  padding: 5px 10px;
  border-radius: 8px;
  margin-right: 10px;
}
@media (max-width: 767px) {
  .zwt1 .tag dd {
    font-size: 12px;
    line-height: 18px;
  }
}
.zwt2 {
  background: #FFF0E0;
}
.zwt2 .nei li {
  background: url(../images/bg_29-5.png) no-repeat left 3px;
  background-size: 22px;
}
@media (max-width: 1200px) {
  .zwt2 .nei li {
    background-size: 18px;
  }
}
@media (max-width: 767px) {
  .zwt2 .nei li {
    background-size: 16px;
  }
}
.zwt3 {
  background: #E5F1F5;
}
.zwt3 .nei li {
  background: url(../images/bg_29-4.png) no-repeat left 3px;
  background-size: 22px;
}
@media (max-width: 1200px) {
  .zwt3 .nei li {
    background-size: 18px;
  }
}
@media (max-width: 767px) {
  .zwt3 .nei li {
    background-size: 16px;
  }
}
.zwt4 .info {
  margin: 0;
  flex-wrap: wrap;
  background: #F7F9FC;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 25px;
  padding-bottom: 0;
}
@media (max-width: 767px) {
  .zwt4 .info {
    margin-bottom: 15px;
  }
}
.zwt4 .info dl {
  width: 100%;
  margin: 0;
  padding: 0;
  border-radius: 0;
  margin-bottom: 15px;
}
.zwt4 .tag {
  display: flex;
  margin-bottom: 5px;
  margin-top: -5px;
}
@media (max-width: 767px) {
  .zwt4 .tag {
    flex-wrap: wrap;
    margin-bottom: 10px;
  }
}
.zwt4 .tag span {
  padding-left: 20px;
  background: url(../images/booth/bg.png) no-repeat 5px center #D6DDEE;
  background-size: 10px;
  line-height: 24px;
  display: inline-block;
  margin-right: 10px;
  border-radius: 4px 4px 4px 4px;
  color: #000000;
  font-size: 12px;
  padding-right: 10px;
  margin-bottom: 10px;
}
@media (max-width: 767px) {
  .zwt4 .tag span {
    margin-right: 5px;
    margin-bottom: 5px;
  }
}
.zwt5 {
  background: #E1E6F1;
  position: relative;
  border-radius: 20px;
  margin: 170px 0 80px 0;
  padding: 40px;
}
@media (max-width: 1200px) {
  .zwt5 {
    margin: 0px 0 70px 0;
    padding: 30px;
  }
}
@media (max-width: 767px) {
  .zwt5 {
    padding: 20px 20px 25px 20px;
    border-radius: 10px;
    margin: -20px 0 40px 0;
  }
}
.zwt5 .img {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 50%;
}
.zwt5 h3 {
  font-size: 24px;
  color: #1D2129;
  line-height: 36px;
  font-weight: bold;
  display: block;
  width: 100%;
  padding-bottom: 20px;
  padding-right: 40%;
}
@media (max-width: 1200px) {
  .zwt5 h3 {
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .zwt5 h3 {
    padding-right: 15%;
    font-size: 18px;
    line-height: 32px;
    padding-bottom: 10px;
  }
}
.zwt5 .more {
  width: 200px;
  line-height: 48px;
  background: #1D6AFF;
  border-radius: 8px;
  display: inline-block;
  color: #fff;
  text-align: center;
  cursor: pointer;
}
.zwt5 .more:hover {
  background: #0048CE;
}
@media (max-width: 1200px) {
  .zwt5 .more {
    width: 170px;
    line-height: 44px;
  }
}
@media (max-width: 767px) {
  .zwt5 .more {
    width: 110px;
    line-height: 32px;
    font-size: 15px;
    border-radius: 5px;
  }
}
