$(function() {
  getBoothUrl(); // 获取展位链接
  getServiceList(); // 获取服务商
  handleSubmit();
  handleService();
})

// 服务商
function getServiceList() {
  $.ajax({
    url: 'https://ocean-api.huicanzhan.cn/official/servicers?page=1&limit=10',
    type: 'get',
    success: function (res) {
      var $ul = $('.fw_list');
      $.each(res.data.items, function(index, item) {
        var $li = `
          <li>
            <a href="javascript:;" class="nei" data-id="${item.servicerId}">
              <p class="plogo"><img src="${ item.logo }" alt="logo"></p>
              <div class="txt">
                <h3 class="name">${ item.company }</h3>
                <div class="type">${ item.scope }</div>
                <div class="desc">
                  ${ item.desc }
                </div>
                <span class="more">去咨询
                  <div class="more-tel">
                    <div>咨询电话：${ item.mobile }</div>
                  </div>
                </span>
              </div>
            </a>
          </li>
        `
        $ul.append($li)
      })
    }
  });
}

function handleService() {
  $(document).on({
    mouseenter: function() {
      $(this).find('.case-qrcode').show();
    },
    mouseleave: function() {
      $(this).find('.case-qrcode').hide();
    }
  }, '.al_list li');
}

function handleSubmit() {
  $(document).on('click', '.btn-submit', function(e) {
    e.preventDefault();
    var name = $('.fw_info .name').val();
    var tel = $('.fw_info .tel').val();
    var company = $('.fw_info .company').val();
    var content = $('.fw_info .content').val();
    if (!name) {
      alert('请输入您的姓名');
      return;
    }
    if (!tel) {
      alert('请输入您的手机号');
      return;
    }
    if (!company) {
      alert('请输入公司名称');
      return;
    }
    if (!content) {
      alert('请输入服务内容');
      return;
    }
    
    $.ajax({
      url: 'https://official-api.huicanzhan.cn/partner-applies',
      type: 'post',
      contentType: "application/json",
      data: JSON.stringify({
        name: name,
        mobile: tel,
        company: company,
        content: content
      }),
      success: function (res) {
        if (res.code) {
          alert(res.message);
          return;
        }
        $('#success-message').fadeIn().delay(2000).fadeOut();
        $('.fw_info .name').val('');
        $('.fw_info .tel').val('');
        $('.fw_info .company').val('');
        $('.fw_info .content').val('');
      }
    });
  });
}

function handleService() {
  $('.fw_list').on('click','.nei', function (e) {
    const id = $(this).context.dataset.id
    window.location.href = `./partnerInfo.html?id=${id}`
  })
}