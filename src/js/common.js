
// 导航
function cNav() {
    var oBody = $("body");
    var oHead = $("#header");
    var oNav = $("#header .nav");
    var oBtn = $("#header .switch");
    var oL = $("#header .nav>li");
    var oTitle = $("#header .nav2 li .title");
    var num = 0;
    var i = 0;
    var oP = $("#placeholder");
    var b = true;
    var t = null;
    // 手机端导航显示
    oBtn.click(function() {
        if (b) {
            b = false;
            t = $(window).scrollTop();
            oBody.addClass('open');
            //oNav.addClass("nav2")
        } else {
            b = true;
            oBody.removeClass('open');
            $(window).scrollTop(t);
            //oNav.removeClass('nav2');
        }
    });

    // 窗口重置隐藏手机端导航
    $(window).resize(function() {
        if ($("#header .nav").css("display") == "block") {
            oBody.removeClass('open');
        }
    });

    // 手机端导航栏目下拉
    oTitle.click(function() {
        $(this).next().stop().slideToggle();
        var style=$(this).parent("li").attr("class")
        if(style!="sel"){
            $(this).parent("li").addClass("sel")
        }
        else{
            $(this).parent("li").removeClass("sel")
        }
    });

    

    // 导航显示及样式
    function fn1() {
        if ($(window).scrollTop() - i > 0 && $(window).scrollTop() > oHead.outerHeight()) {
            i = $(window).scrollTop();
            //oHead.addClass("head-move");
            oHead.addClass("style2");
        } else if ($(window).scrollTop() - i <= 0) {
            i = $(window).scrollTop();
            //oHead.removeClass("head-move");
            if ($(window).scrollTop() <= oHead.outerHeight() && oP.length == 0 && !oBody.hasClass("open")) {
                oHead.removeClass("style2");
            } else if ($(window).scrollTop() > oHead.outerHeight()) {
                oHead.addClass("style2");
            } else if ($(window).scrollTop() <= oHead.outerHeight() && oP.length != 0) {
                oHead.addClass("style2");
            }
        }
    }
    fn1();
    $(window).scroll(function() {
        fn1();
    });

    // pc导航动画
    oL.each(function() {
        if ($(this).hasClass("on")) {
            num = $(this).index();
        }
        $(this).hover(function() {
                // oL.eq(num).removeClass("on");
                $(this).children("ul").stop().slideDown();
                $(this).children(".la_zs").stop().slideDown(400);
            },
            function() {
                // oL.eq(num).addClass("on");
                $(this).children("ul").stop().slideUp();
                $(this).children(".la_zs").stop().slideUp(0);
            });
    });

}
cNav();


// 可视化数据滚动
function visualData(obj) {
    $(document).ready(function() {
        obj.each(function() {
            var h = Number($(this).html());
            var t = "";
            var n = Math.ceil(h / 25);
            var a = true;
            var This = $(this);
            if ($(this).length != 0) {
                t = $(this).offset().top;
            }
            This.html(0);
            fn1();
            $(window).scroll(function() {
                fn1();
            });

            function fn1() {
                var wT = $(window).scrollTop();
                if (wT > t - $(window).height() + 50 && wT < t - 50 && a == true) {
                    a = false;
                    var y = 0;
                    var timer2 = setInterval(function() {
                        if (y >= h) {
                            y = h;
                            clearInterval(timer2);
                        }
                        This.html(y);
                        y += n;
                    },100);
                }
            }
        });
    });
}
// visualData($(".num-move"));

function visualData2(obj) {
    $(window).load(function() {
        obj.each(function() {
            var h = Number($(this).html());
            var t = "";
            var n = Math.ceil(h / 25);
            var a = true;
            var This = $(this);
            if ($(this).length != 0) {
                t = $(this).offset().top;
            }
            This.html(0);
            fn2();
            $(window).scroll(function() {
                fn2();
            });

            function fn2() {
                var wT = $(window).scrollTop();
                if (wT > t - $(window).height() + 50 && wT < t - 50 && a == true) {
                    a = false;
                    var y = 0;
                    var timer2 = setInterval(function() {
                        if (y >= h) {
                            y = h;
                            clearInterval(timer2);
                        }
                        This.html(y);
                        y += n;
                    }, 500);
                }
            }
        });
    });
}



// 侧边栏回到顶部
function goTop() {
    var obj = $("#go-top");
    var oBtn = $("#go-top");
    oBtn.click(function() {
        $("html,body").animate({ scrollTop: 0 }, 500);
    });

    function fn1() {
        if ($(window).scrollTop() > $(window).height()) {
            obj.fadeIn();
        } else {
            obj.fadeOut();
        }
    }
    fn1();
    $(window).scroll(function() {
        fn1();
    });
}
goTop();

// 底部导航
function footerNav() {
    var aList = $("#footer .list-box");
    if ($(window).width() <= 767) {
        aList.each(function() {
            var This = $(this);
            $(this).find(".title-box").click(function() {
                This.toggleClass("on");
                This.find(".list").stop().slideToggle();
            });
        });
    }
}
footerNav();

// 点击底部微信弹出二维码
function weixin() {
    var b = $("#footer .wx-btn");
    var w = $("#footer .weixin");
    var d = w.find(".img-box");
    b.click(function() {
        w.stop().fadeToggle();
    });
    d.click(function() {
        return false;
    });
    w.click(function() {
        w.stop().fadeToggle();
    });
}
weixin();



// 页面进入动画
(function($) {
    function wow() {
        var t = .1,
            e = !0,
            wow = new WOW({
                boxClass: "wow",
                animateClass: "animated",
                offset: 10,
                callback: function(e) {
                    (e = $(e)).css("animation-delay", t.toFixed(1) + "s"),
                        t += .1;
                }
            });
        $(window).scroll(function() {
                e && (t = .2, setTimeout(function() {
                    e = !0
                }, 150), e = !1)
            }),
            wow.init();
    }
    wow();
}(jQuery));

