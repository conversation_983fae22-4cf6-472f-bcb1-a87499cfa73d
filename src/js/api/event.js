$(function () {
  getBoothUrl(); // 获取展位链接

  let event_id = '' 
  event_id = getUrlParam('eventId');
  getEventInfo(event_id)
})
const defaultLogo = 'https://i.huicanzhan.cn/pi.png'
// 获取活动详情
function getEventInfo(event_id) {
  $.ajax({
    url: 'https://official-api.huicanzhan.cn/events/' + event_id,
    type: 'get',
    success: function (res) {
      const { event, sponsor } = res;
      if (event.event_id === 0) {
        window.location.href = "./"
      }
      if (event.intro) {
        event.intro = event.intro.replace(
          /\<img/gi,
          '<img style="max-width:100%;height:auto" '
        );
      }

      $('.pimg img').attr('src', event.cover);
      $('.xq_info .title').text(event.subject);
      $('.xq_info .date').text(`时间：${moment(event.start_at * 1000).format('yyyy-MM-DD HH:mm')} ~ ${moment(event.finish_at * 1000).format('yyyy-MM-DD HH:mm')}`);
      $('.xq_info .scale').text(`规模：${event.number}人`);
      $('.xq_info .adress').text(`地址： ${ event.province } ${ event.city } ${ event.address }`);
      $('.xq_info .user img').attr('src', sponsor.logo || defaultLogo);
      $('.xq_info .user span').text(sponsor.company);
      $('.xq_info .a_bm').attr('href', event.h5_signin_url + '?channel=hzc_website');
      // $('.xq_info .a_bm').attr('href', './y/'+event_id);
      $('#share .wechat-qrcode img').attr({
        src: `data:image/png;base64,${event.qrcode}`,
        width: '100%'
      });
      if (event.intro === "") {
       $('.xq_desc').remove();
      } else {
       $('.xq_desc .desc').html(event.intro);
      }
    }
  })
}