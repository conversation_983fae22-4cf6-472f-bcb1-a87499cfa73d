@charset "utf-8";
@charset "utf-8";

// 公共响应变量
@min-xs: (min-width: 768px);
@min-sm: (min-width: 1200px);
@min-lg: (min-width: 1600px);
@min-xg: (min-width: 1920px);

@max-xs: (max-width: 767px);
@max-sm: (max-width: 1200px);
@max-md: (max-width: 1320px);
@max-lg: (max-width: 1580px);
@max-xg: (max-width: 1900px);

// 初始化
* {
    padding: 0;
    margin: 0;
    outline: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
html {
    font-size: 16px;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
    
}
body {
    min-height: 100%;
    font-family: v-sans, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    color: #1D2129;
    font-size: 16px;
    @media @max-sm {
        font-size: 15px;
    }
}
li { list-style: none;}
h1,h2,h3,h4,h5,h6 { font-weight: normal;}
a,a:hover,a:focus { text-decoration: none;}
input,button { border-radius: 0;}
input[type="button"],input[type="reset"],input[type="submit"] { -webkit-appearance: button; cursor: pointer;}
textarea {resize: none; overflow: auto;}
input,button,textarea,select { border: 0; font-family: inherit;font-size: inherit;color: inherit;background: transparent;}
select {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    white-space: nowrap;
    //-webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}
select::-ms-expand {
    display: none;
}
table {
    border-collapse: collapse;
}
img {
    vertical-align: top;
    transition: 0.5s;
}
a{transition: 0.5s;}

// 公共浮动、清浮动
.fl { float: left;}
.fr {float: right;}
.clear {zoom: 1;}
.clear:after { content: '';display: block; clear: both;}

// 公共内容区
.container {
    margin: 0 auto;
    width: 88%;
    max-width: 1200px;
    @media @max-sm {
        width: 90%;
    }
    @media @max-xs {
        padding: 0 15px;
        width: 100%;
    }
}
.pt20{
    padding-top: 20px;
    @media @max-sm {
        padding-top: 15px;
    }
    @media @max-xs {
        padding-top: 10px;
    }
}

.pt30{
    padding-top: 30px;
    @media @max-sm {
        padding-top: 20px;
    }
    @media @max-xs {
        padding-top: 10px;
    }
}
.pb50{
    padding-bottom: 50px;
    @media @max-sm {
        padding-bottom: 30px;
    }
    @media @max-xs {
        padding-bottom: 20px;
    }
}
.pb80{
    padding-bottom: 80px;
    @media @max-sm {
        padding-bottom: 50px;
    }
    @media @max-xs {
        padding-bottom: 30px;
    }
}
.ptb80{
    padding: 80px 0;
    @media @max-sm {
        padding: 50px 0;
    }
    @media @max-xs {
        padding: 30px 0;
    }
}
.pb140{
    padding-bottom: 140px;
    @media @max-sm {
        padding-bottom: 100px;
    }
    @media @max-xs {
        padding-bottom: 30px;
    }
}
.ptb30{
    padding-top: 30px;
    padding-bottom: 30px;
    @media @max-sm {
        padding-top: 20px;
        padding-bottom: 20px;
    }
    @media @max-xs {
        padding-top: 10px;
        padding-bottom: 10px;
    }
}
.pt40{
    padding-top: 40px;
    @media @max-sm {
        padding-top: 30px;
    }
    @media @max-xs {
        padding-top: 20px;
    }
}
.pt70{
    padding-top: 70px;
    @media @max-sm {
        padding-top: 40px;
    }
    @media @max-xs {
        padding-top: 20px;
    }
}

// 公共颜色
@color1:#1D6AFF;
@color2:#EB7F2F;
@color3:#0048CE;


// 公共背景色
.bg_hui{ background: #F3F3F3;}
.bg_bai{ background: #fff;}


/* 公共标题 */
.title_img{
    text-align: center;
    padding: 100px 0;
    img{max-height: 110px;}
    @media @max-sm {
        padding: 60px 0;
        img{max-height: 80px;}
    }
    @media @max-xs {
        padding: 30px 0;
        img{max-height: 56px;}
    }
}



/*顶部*/
#header {
    position:sticky;
    top: 0;
    left: 0;
    z-index: 900;
    width: 100%;
    line-height: 60px;
    text-align: center;
    color: #fff;
    transition: all 0.5s;
    background: #fff;
    height: 60px;
    -moz-box-shadow:0px 0px 10px rgba(0,0,0,0.2);
    -webkit-box-shadow:0px 0px 10px rgba(0,0,0,0.2);
    box-shadow:0px 0px 10px rgba(0,0,0,0.2);
    @media @max-lg {
        font-size: 14px;
    }
    @media @max-sm {
        position:sticky;
    }
    a {
        color: #1D2129;
        transition: all 0.5s;
    }
    .container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        @media @max-sm {
            padding: 0 20px;
            width: 100%;
        }
        @media @max-xs {
            padding: 0 15px;
        }
    }
    .right-box {
        display: flex;
        align-items: center;
    }

    /*顶部logo*/
    .logo{
        display: flex;
        align-items: center;
        flex-wrap: 0;
        .img-box {
            display: flex;
            align-items: center;
            height: 60px;
        }
        img {
            max-height: 40px;
            @media @max-xs {
                max-height: 34px;
            }
        }
    }

    /*顶部pc导航*/
    .nav {
        display: flex;
        align-items: center;
        font-size: 16px;
        height: auto;
            @media @max-xs {display: none;
            }
        li {
            position: relative;
            display: flex;
            justify-content: center;
            align-content: center;
            padding: 0 20px;
            @media @max-sm {
                padding: 0 10px;
            }
            .tit{
                background: url(../images/jt.png) no-repeat right center;
                padding-right: 15px;
                background-size: 10px;
            }
            a{ 
                font-size: 16px; white-space: nowrap;
                cursor: pointer;
                @media @max-md {
                    font-size: 15px;
                }
            }
            &:last-child{ padding-right: 0;}
        }
        .li{
            padding-left: 100px;
            display: flex;
            align-items: center;
            @media @max-sm {
                padding-left: 30px;
            }
            @media (min-width: 1000px) {
                padding-left: 70px;
            }
            .a_fb{
                background: @color1;
                color: #fff;
                line-height: 36px;
                border-radius: 8px;
                padding: 0 20px;
                margin-left: 20px;
                @media @max-sm {
                    margin-left: 15px;
                    padding: 0 15px;
                }
                &:hover{
                    background: @color3;
                }
            }
        }
        
        ul {
            position: absolute;
            top: 99%;
            left: 50%;
            display: none;
            width: 130px;
            line-height: 46px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, .2);
            -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
            background: #fff;
            li{
                border-bottom: 1px solid #fff;
                padding: 0;
                position: relative;
                a {
                    color: #000;
                    font-size: 15px;
                }
                &:hover,&.sel{
                    dl{ display: block;}
                    background:#F3F3F5;
                    a{color: @color1 !important;}
                }
            }
        }
        
    }
}
#header .nav li.on>a,#header .nav li:hover>a{color: @color1; }
#header.style2 .nav>li>a::before {background: @color1;}

/*顶部移动端导航*/
#header  {
    .nav2 {
        position: absolute;
        top: 60px;
        left: 0;
        width: 100%;
        height: 0;
        line-height: 50px;
        background: #FFFFFF !important;
        overflow-y: auto;
        transition: all 0.5s;
        text-align: left;
        color: #000;
        flex-wrap: wrap;
        align-items: flex-start;
        li {
            border-bottom: 1px solid #D6D9DE;
            transition: 0.5s;
            @media @max-xs {
                padding: 0 20px;
            }
            .title{
                display: flex;
                align-items: center;
                justify-content: space-between;
                background:url(../images/jt4.png) no-repeat right center;
                background-size: 12px;
                font-size: 16px;
            }
            &.sel{
                .title{
                    background:url(../images/jt5.png) no-repeat right center;
                    background-size: 12px;
                }
            }
            a {
                color: #000;
                -webkit-transition: all 0.3s;
                transition: all 0.3s;
                font-size: 16px;
                width: 100%;
                display: block;
            }
            a:hover,&.on>a {
                color: @color1
            }
            ul {
                display: none;
                li{
                    border: 0;
                }
                a{
                    padding: 0 15px;
                }
            }
        }
    }
}
.open #header .nav2 {height: calc(100vh - 60px);  }

/*顶部导航开关*/
#header {
    .switch {
        display: none;
        width: 24px;
        height: 20px;
        cursor: pointer;
        @media @max-xs {
            display: block;
        }
        i {
            position: relative;
            display: block;
            height: 2px;
            background: #333333;
            -webkit-transition: all 0.3s;
            transition: all 0.3s;
            &:nth-child(1) {
                top: 0;
            }
            &:nth-child(3) {
                bottom: 0;
            }
            &:nth-child(2) {
                margin: 6px 0;
            }
        }
    }
}

#header.style2 .c-switch i {
    background: #fff;
}
body.open #header{
    .switch i:nth-child(2) {
        opacity: 0;
    }
    .switch i:nth-child(1) {
        top: 8px;
        -webkit-transform: rotate(45deg);
        transform: rotate(45deg);
    }
    .switch i:nth-child(3) {
        bottom: 8px;
        -webkit-transform: rotate(-45deg);
        transform: rotate(-45deg);
    }
} 

/* 底部 */
#footer{
    background: #1F2936;
    padding-top: 50px;
    overflow: hidden;
    @media @max-sm {
        padding-top: 40px;
    }
    @media @max-xs {
        padding-top: 20px;
    }
    .container{
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
    }
    .foot_one{
        display: flex;
        color: #fff;
        max-width: 60%;
        @media @max-sm {
            max-width: 70%;
        }
        @media @max-xs {
            max-width: 100%;
            width: 100%;
            flex-wrap: wrap;
        }
        a{
            color: #fff;
            font-size: 14px;
            line-height: 34px;
            color: #C9CDD4;
            &:hover{
                color: #fff;
            }
        }
        li{
            padding-right: 90px;
            @media @max-sm {
                padding-right: 60px;
            }
            @media @max-xs {
                padding-right: 0px;
                width: 100%;
                display: flex;
                flex-wrap: wrap;
                padding-bottom: 14px;
            }
            h3{
                line-height: 40px;
                font-size: 16px;
                color: @color1;
                width: 100%;
                @media @max-xs {
                    font-size: 14px;
                    line-height: 22px;
                    color: #74A3FF;
                }
            }
            p{
                padding-right: 15px;
                @media @max-xs {
                    a{
                        font-size: 16px;
                    }
                }
            }
        }
    }
    .foot_two{
        color: #fff;
        width: 240px;
        @media @max-sm {
            width: 220px;
        }
        h6{
            line-height: 30px;
            font-size: 14px;
        }
        .tell{
            font-size: 20px;
        }
        dl{
            padding-top: 26px;
            display: flex;
            justify-content: space-between;
            @media @max-xs {
                display: none;
            }
            dd{
                text-align: center;
                font-size: 12px;
                color: #C3C3C3;
                line-height: 34px;
            }
            img{
                width: 100px;
                border: 4px;
                @media @max-sm {
                    width: 90px;
                }
                @media @max-xs {
                    
                }
            }
        }
    }
    .foot_three{
        width: 100%;
        display: block;
        border-top: 1px solid #494D55;
        margin-top: 50px;
        padding: 15px 0;
        line-height: 30px;
        font-size: 12px;
        color: #86909C;
        @media @max-xs {
            margin-top: 20px;
            line-height: 20px;
        }
    }
}


/* 顶部banner */
.banner {
    position: relative;
    overflow: hidden;
    img {
        width: 100%;
        display: block;
    }
    .pc_img{
        display: block;
        width: 100%;
        @media @max-xs {
            display:none;
        }
    }
    .yd_img{
        display:none;
        width: 100%;
        @media @max-xs {
            display: block;
        }
    }
}
.ind_banner{ 
    background: linear-gradient(to bottom, #B7D5FF, #DDEEFF);
    display:block; 
    width:100%;
    .next,.prev{ 
        width:20px; height:32px; position:absolute; top:50%; z-index:100;
        transform: translate(0, -50%); outline:none; cursor:pointer; opacity: 0;
        transition: 0.5s;
        border-radius: 4px;
        overflow: hidden;
    }
    .next{right:20px; background:url(../images/right.png) no-repeat center center; background-size: 100% 100%;}
    .prev{left:20px;background:url(../images/left.png) no-repeat center center;  background-size: 100% 100%;}
    &:hover{
        .next,.prev{
            opacity: 1;
        }
    }
    .container{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 0;
        @media @max-xs {
            padding: 10px 15px;
        }
        @media @max-sm {
            height: 360px;
        }
        @media @max-sm {
            height: 300px;
        }
        @media @max-xs {
            height: 200px;
            overflow: hidden;
        }
    }
    .nei{
        @media @max-sm {
            padding-right: 40px;
        }
        @media @max-xs {
            padding-right: 0px;
        }
        .title{
            font-size: 36px;
            line-height: 50px;
            color: #1D2129;
            font-weight: 600;
            @media @max-sm {
                font-size: 32px;
                line-height: 44px;
                width: 170%;
            }
            @media @max-xs {
                width: 100%;
                font-size: 17px;
                line-height: 24px;
            }
        }
        .desc{
            line-height: 24px;
            font-size: 16px;
            padding: 10px 0 20px 0;
            @media @max-sm {
                width: 170%;
            }
            @media @max-xs {
                width: 100%;
                font-size: 12px;
                line-height: 20px;
                padding: 5px 0 10px 0;
            }
            p{
                padding: 8px 0;
                font-size: 18px;
                max-width: 90%;
                line-height: 28px;
            }
        }
        .more{
            width: 200px;
            line-height: 48px;
            background: @color1;
            border-radius: 8px;
            display: inline-block;
            color: #fff;
            text-align: center;
            cursor: pointer;
            &:hover{
                background: @color3;
            }
            @media @max-sm {
                width: 170px;
                line-height: 44px;
            }
            @media @max-xs {
                width: 110px;
                line-height: 32px;
                font-size: 15px;
                border-radius: 5px;
            }
        }
    }
    .img{
        height: 300px;
        @media @max-sm {
            max-height: 240px;
        }
        @media @max-xs {
            max-height: 100px;
            margin-right: -15px;
        }
    }
    .pimg{
        width: 100%;
        padding-bottom: 20px;
        @media @max-xs {
            padding-bottom: 10px;
        }
    }
    .img_title{
        width: 100%;
        @media @max-xs {
            width: 180%;
        }
    }
    
    .time{
        height: 63px;
        margin-bottom: -15px;
        @media @max-sm {
            height: 50px;
            margin-bottom: -10px;
        }
        @media @max-xs {
            height: 36px;
        }
    }
    .pc_img{
        display: block;
        width: 100%;
        @media @max-xs{
            display: none;
        }
    }
    .yd_img{
        display: none;
        width: 100%;
        @media @max-xs{
            display: block;
        }
    }
    .type{
        height: 36px;
        margin-bottom: 10px;
        @media @max-sm{
            height: 30px;
        }
        @media @max-xs{
            height: 26px;
        }
    }
}
.partner_banner,.case_banner{
    .container{
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 400px;
        @media @max-sm {
            height: 360px;
        }
        @media @max-sm {
            height: 300px;
        }
        @media @max-xs {
            height: 400px;
            overflow: hidden;
            flex-wrap: wrap;
            padding: 20px 15px;
        }
    }
    .itme1{
        background: linear-gradient(to bottom, #8EC6FF,#B8DCFF);
    }
    .itme2{
        background: linear-gradient(to bottom, #93AFFF,#BECFFF);
    }
    .itme3{
        background: linear-gradient(to bottom, #87C9E3,#ADE4EE);
    }
    .nei{
        @media @max-xs {
            width: 100%;
        }
        .desc{
            line-height: 24px;
            font-size: 16px;
            padding: 10px 0 20px 0;
            @media @max-xs {
                padding-bottom: 0;
            }
            p{
                @media @max-xs {
                    padding: 4px 0;
                    font-size: 14px;
                    max-width: 90%;
                    line-height: 22px;
                }
            }
        }
        
        .title{
            @media @max-xs {
                width: 100%;
                font-size: 19px;
                line-height: 26px;
            }
        }
    }
    
    .img{
        height: 300px;
        @media @max-sm {
            max-height: 240px;
        }
        @media @max-xs {
            max-height: 400px;
            width: 100%;
            height: auto;
            display: block;
            margin: 0;
        }
    }
}

.case_banner{
    .container{
        @media @max-xs {
            height: 440px;
        }
    }
    .type_tit{
        line-height: 36px;
        padding: 0 18px;
        border-radius: 5px;
        color: #fff;
        font-size: 18px;
        display: inline-block;
        background: #4390E2;
        margin-bottom: 10px;
        
        @media @max-xs {
            line-height: 28px;
            font-size: 14px;
            padding: 0 10px;
        }
    }
    .itme1{
        background: linear-gradient(to bottom, #A6D3FF,#D4E1FF);
    }
    .itme2{
        background: linear-gradient(to bottom, #FFD5AD,#FFE5AB);
        .type_tit{
            background: #F59614;
            color: #000;
        }
    }
    .itme3{
        background: linear-gradient(to bottom, #C3CFE9,#DAE7F2);
        .type_tit{
            background: #566E9F;
        }
    }
}


/*首页轮播图*/
.bannerLB {
    position: relative;
    .swiper-slide {
        overflow: hidden;
        background: #fff;
        img {
            width: 100%;
            height: 100vh;
            object-fit: cover;
            @media @max-sm {
                height: 60vh;
            }
        }
        .yd{
            display: none;
            
            @media @max-sm {
                display: block;
            }
        }
        .pc {
            @media @max-sm {
                display: none;
            }
        }
    }
    .pagination{
        position: absolute;
        bottom: 10vh;
        left: 0;
        z-index: 100;
        text-align: center;
        .swiper-pagination-bullet{
            width: 28px;
            height: 20px;
            background: url(../images/bg_01.png) no-repeat;
            background-size: 100% 100%;
            opacity: 1;
            margin: 0 12px;
            @media @max-sm {
                width: 26px;
                height: 18px;
                margin: 0 8px;
            }
            @media @max-xs {
                width: 18px;
                height: 12px;
                margin: 0 5px;
            }
        }
        .swiper-pagination-bullet-active{
            background: url(../images/bg_01-sel.png) no-repeat;
            background-size: 100% 100%;
        }
        @media @max-sm {
            bottom: 50px;
        }
        @media @max-xs {
            bottom: 35px;
        }
    }
}

/* 首页 */
.ind_bg{
    background: #F5F5F7;
}
.ind_one{
    display: flex;
    justify-content: space-between;
    margin-top: -40px;
    position: relative;
    z-index: 10;
    @media @max-xs {
        margin-top: 15px;
    }
    li{
        width: 32%;
        background: #fff;
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 80px;
        box-shadow: 0px 20px 30px 0px rgba(0,0,0,0.05);
        border-radius: 12px 12px 12px 12px;
        @media @max-xs {
            width: 31%;
            height: 120px;
            flex-wrap:wrap;
            text-align: center;
            justify-content:center;
            padding: 10px;
        }
    }
    img{
        height: 46px;
        @media @max-xs {
            height:40px;
        }
    }
    .txt{
        padding-left: 10px;
        font-size: 14px;
        line-height: 20px;
        font-weight: 500;
        @media @max-xs {
            font-size: 13px;
            width: 100%;
            padding-left: 0px;
        }
        strong{
            font-size: 22px;
            line-height:28px;
            @media @max-xs {
                font-size:18px;
                line-height:34px;
            }
        }
    }
}
.ind_title{
    font-weight: 600;
    font-size: 36px;
    color: #1D2129;
    line-height: 100px;
    text-align: center;
    letter-spacing: 2px;
    @media @max-sm {
        font-size: 28px;
        line-height: 80px;
    }
    @media @max-xs {
        font-size: 20px;
        line-height: 60px;
        letter-spacing: 1px;
    }
    
}
.ind_two{
    display: flex;
    justify-content: center;
    margin: 0 -10px;
    @media @max-sm {
        margin: 0 -6px;
    }
    @media @max-xs {
        flex-wrap: wrap;
    }
    li{
        width: 25%;
        padding: 0 10px;
        transition: 0.4s;
        @media @max-sm {
            padding: 0 8px;
        }
        @media @max-xs {
            padding:0 6px 12px 6px;
        }
        &:hover{
            margin-top: -15px;
            .nei{
                color: @color1;
            }
        }
    }
    .nei{
        display: block;
        background: #fff;
        padding: 12px;
        box-shadow: 0px 20px 30px 0px rgba(0,0,0,0.05);
        border-radius: 12px 12px 12px 12px;
        text-align: center;
        color: #333;
        a{
          color: #333;
          &:hover{
            color: @color1;
          }
        }
        img{
            height: 60px;
            @media @max-sm {
                height: 50px;
            }
            @media @max-xs {
                height: 42px;
            }
        }
        p{
            line-height: 24px;
            padding-top: 10px;
            @media @max-xs {
                line-height: 20px;
                font-size: 14px;
            }
        }
    }
}
.ind_activity{
    position: relative;
    .list{
        display: flex;
        flex-wrap: wrap;
        margin: 0 -10px;
        @media @max-xs {
            margin: 0 ;
            justify-content: space-between;
        }
    }
    li{
        padding:0 10px 20px 10px;
        width: 33.333%;
        @media @max-xs {
            width: 48.2%;
            padding: 0 0 15px 0;
            &:nth-child(9){ display: none;}
        }
    }
    .nei{
        display: block;
        width: 100%;
        background: #FFFFFF;
        border-radius: 12px;
        padding: 12px;
        transition: 0.5s;
        @media @max-xs {
            padding: 8px;
            border-radius: 8px;
        }
        &:hover{
            -moz-box-shadow:0px 0px 10px rgba(0,0,0,0.2);
            -webkit-box-shadow:0px 0px 10px rgba(0,0,0,0.2);
            box-shadow:0px 0px 10px rgba(0,0,0,0.2);
            .pimg img{
                -webkit-transform:scale(1.04);
                -moz-transform:scale(1.04);
                transform:scale(1.04); 
            }
        }
    }
    .pimg{
        width: 100%;
        height: 240px;
        overflow: hidden;
        border-radius: 8px;
        @media @max-sm {
            height: 120px;
        }
        @media @max-xs {
            border-radius: 6px;
            height: 90px;
        }
        img{
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
    .time{
        font-weight: 500;
        font-size: 14px;
        color: #1D6AFF;
        line-height: 22px;
        padding: 10px 0 5px 0;
        @media @max-xs {
            font-size: 12px;
            padding: 5px 0 2px 0;
        }
    }
    .title{
        font-weight:600;
        font-size: 16px;
        color: #1D2129;
        line-height: 24px;
        overflow: hidden;text-overflow: ellipsis;display: -webkit-box;
        -webkit-line-clamp: 2;-webkit-box-orient: vertical;
        height: 48px;
        margin-bottom: 8px;
        @media @max-xs {
            font-size: 15px;
            line-height: 20px;
            height: 40px;
            margin-bottom: 5px;
        }
    }
    .address{
        padding-left: 18px;
        background: url(../images/bg_01.png) no-repeat left 4px;
        background-size: 10px;
        font-size: 14px;
        color: #4E5969;
        line-height: 22px;
        overflow: hidden;text-overflow: ellipsis;display: -webkit-box;
        -webkit-line-clamp: 1;-webkit-box-orient: vertical;
        @media @max-xs {
            font-size: 12px;
        }
    }
    .fy{
        position:absolute;
        top: -50px;
        right: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        @media @max-xs {
            top: -40px;
        }
        .next,.prev{
            width: 24px;
            height: 24px;
            background: #fff;
            display: inline-block;
            border-radius: 40px;
            cursor: pointer;
        }
        .next{
            background: url(../images/left2.png) no-repeat center center #fff;
            background-size: 7px;
            &:hover{
                background: url(../images/left2-sel.png) no-repeat center center @color1;
                background-size: 8px;
            }
        }
        .prev{
            background: url(../images/right2.png) no-repeat center center #fff;
            background-size: 7px;
            &:hover{
                background: url(../images/right2-sel.png) no-repeat center center @color1;
                background-size: 8px;
            }
        }
        .swiper-pagination-fraction{
            width: auto;
            bottom: 0;
            padding: 0 20px;
            @media @max-xs {
                padding: 0 10px;
            }
        }
    }
}
.ind_more{
    line-height: 30px;
    text-align: center;
    a{
        padding-right: 15px;
        background: url(../images/right2.png) no-repeat right center;
        color: #000000;
        &:hover{
            color: @color1;
            background: url(../images/right3.png) no-repeat right center;
        }
    }
}
.ind_case{
    display: flex;
    background: #fff;
    border-radius: 20px;
    padding: 40px;
    align-items: flex-start;
    margin-bottom: 20px;
    @media @max-sm {
        padding: 30px;
    }
    @media @max-xs {
        flex-wrap: wrap;
        background: none;
        padding: 0;
    }
    .list{
        margin-bottom: -24px;
        padding-right: 40px;
        @media @max-sm {
            padding-right: 30px;
            margin-bottom: 0px;
        }
        @media @max-xs {
            width: 100%;
            padding:0;
        }
        li{
            padding-bottom: 24px;
            @media @max-xs {
                padding: 10px;
                background: #fff;
                border-radius: 6px;
                margin-bottom: 15px;
            }
        }
        .nei{
            display: flex;
            width: 100%;
            align-items: flex-start;
            &:hover{
                .pimg img{
                    -webkit-transform:scale(1.1);
                    -moz-transform:scale(1.1);
                    transform:scale(1.1);
                }
                .txt .tit{
                    color: @color1;
                }
            }
        }
        .pimg{
            width: 160px;
            height: 120px;
            border-radius: 8px;
            overflow: hidden;
            flex-shrink: 0;
            @media @max-sm {
                width: 120px;
                height: 90px;
            }
            @media @max-xs {
                border-radius: 5px;
            }
            img{
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
        .txt{
            padding-left: 15px;
            .tit{
                font-weight: 500;
                font-size: 16px;
                color: #1D2129;
                line-height: 24px;
                margin-bottom: 8px;
                font-weight: bold;
                @media @max-sm {
                    margin-bottom: 5px;
                }
            }
            .desc{
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 4;
                -webkit-box-orient: vertical;
                font-size: 14px;
                color: #4E5969;
                line-height: 22px;
                @media @max-sm {
                    -webkit-line-clamp: 3;
                    line-height: 21px;
                }
            }
        }
    }
    .right{
        width: 38.5%;
        flex-shrink: 0;
        border-radius: 10px;
        overflow: hidden;
        position: relative;
        background: #E1E6F1;
        @media @max-sm {
            width: 42%;
        }
        @media @max-xs {
            width: 100%;
        }
        .img{
            display: block;
            width: 100%;
        }
        .txt{
            position: absolute;
            bottom: 20px;
            background: #fff;
            left: 4.5%;
            width: 91%;
            padding:20px 30px;
            border-radius: 8px;
            @media @max-sm {
                bottom: 12px;
                padding:15px 20px;
            }
            @media @max-xs {
                
            }
            dt{
                line-height: 40px;
                font-size: 24px;
                color: #1D2129;
                font-weight: bold;
                @media @max-sm {
                    line-height: 30px;
                    font-size: 20px;
                }
                @media @max-xs {
                    
                }
            }
            dd{
                line-height: 30px;
                font-size: 14px;
                color: #4E5969;
                @media @max-sm {
                    line-height: 26px;
                    font-size: 13px;
                }
                @media @max-xs {
                    
                }
            }
            .bottom{
                display: flex;
                padding-top: 15px;
                @media @max-sm {
                    padding-top: 10px;
                }
                @media @max-xs {
                    
                }
                a{
                    display: inline-block;
                    border: 1px solid @color1;
                    width: 104px;
                    border-radius: 8px;
                    margin-right: 15px;
                    line-height: 36px;
                    text-align:center;
                    color: @color1;
                    font-size: 16px;
                    &:hover{
                        background: @color1;
                        color:#fff;
                    }
                    @media @max-sm {
                        width: 90px;
                        border-radius: 6px;
                        font-size: 15px;
                        line-height: 32px;
                    }
                    @media @max-xs {
                        
                    }
                }
                .a1{
                    background: @color1;
                    color:#fff;
                    &:hover{
                        background: @color3;
                        color:#fff;
                    }
                }
            }
        }
    }
}
.ind_three{
    background: #E1E6F1;
    border-radius: 20px;
    padding: 20px 40px;
    margin-bottom: 80px;
    @media @max-sm {
        border-radius: 15px;
        padding: 10px 30px;
        margin-bottom: 60px;
    }
    @media @max-xs {
        margin: 0 -15px;
        border-radius: 0;
        padding: 10px;
        margin-bottom: 0px;
    }
    .list{
        display: flex;
        margin: 0 -10px;
        flex-wrap: wrap;
        padding-bottom: 20px;
        @media @max-sm {
            margin: 0 -8px;
            padding-bottom: 10px;
        }
        @media @max-xs {
            margin: 0 -3px;
            justify-content: center;
        }
        li{
            padding: 10px;
            width: 20%;
            @media @max-sm {
                padding: 5px;
            }
            @media @max-xs {
                width: 33.33%;
            }
        }
        a{
            display:block;
            width: 100%;
            background: #fff;
            border-radius: 8px;
            height: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: 0.3s;
            @media @max-sm {
                height: 54px;
            }
            @media @max-xs {
                height: 40px;
                padding: 5px;
                border-radius: 5px;
            }
            img{
                max-width: 100%;
                max-height: 100%;
            }
            &:hover{
                -moz-box-shadow:0px 0px 10px rgba(0,0,0,0.2);
                -webkit-box-shadow:0px 0px 10px rgba(0,0,0,0.2);
                box-shadow:0px 0px 10px rgba(0,0,0,0.2);
                margin-top: -5px;
            }
        }
    }
    .imgBanner{
        display: block;
        width: 100%;
        border-radius: 10px;
        margin: 10px 0;
        @media @max-xs {
            display: none;
        }
    }
}

/* 展商管理 */
.gl_banner{
    background: url(../images/bg_02.png) no-repeat center center;
    background-size: cover;
    padding: 110px 0;
    overflow: hidden;
    @media @max-sm {
        padding: 70px 0;
    }
    @media @max-xs {
        padding: 40px 0;
    }
    .title{
        font-size: 60px;
        font-weight: bold;
        line-height: 100px;
        @media @max-sm {
            font-size: 38px;
            line-height: 60px;
        }
        @media @max-xs {
            font-size: 22px;
            line-height: 40px;
        }
    }
    .desc{
        font-size: 36px;
        color: #FE7700;
        line-height: 66px;
        @media @max-sm {
            font-size: 26px;
            line-height: 44px;
        }
        @media @max-xs {
            font-size: 18px;
            line-height: 30px;
            padding-top: 5px;
        }
    }
    dl{
        display: flex;
        padding: 20px 0 60px 0;
        @media @max-sm {
            padding: 20px 0 40px 0;
        }
        @media @max-xs {
            padding: 15px 0 30px 0;
        }
        dd{
            min-width: 220px;
            border: 1px solid @color1;
            border-radius: 40px;
            line-height: 58px;
            text-align: center;
            margin-right: 20px;
            color: #41A5FD;
            font-size: 24px;
            font-weight: bold;
            @media @max-sm {
                font-size: 18px;
                line-height: 46px;
                min-width: 150px;
            }
            @media @max-xs {
                font-size: 15px;
                line-height: 34px;
                min-width: 0;
                padding: 0 20px;
                white-space: nowrap;
                margin-right: 10px;
            }
        }
    }
    .more{
        width: 380px;
        line-height: 68px;
        background: #EB7F2F;
        display: inline-block;
        text-align: center;
        font-size: 26px;
        color: #fff;
        font-weight: bold;
        letter-spacing: 2px;
        @media @max-sm {
            font-size: 20px;
            width: 260px;
            line-height: 52px;
        }
        @media @max-xs {
            font-size: 17px;
            width: 180px;
            line-height: 40px;
        }
    }
}
.gl_one{
    text-align: center;
    background: linear-gradient(to bottom, #F8FAFF, #fff);
    img{
        width: 94%;
        max-width: 1200px;
        @media @max-xs {
            width: 90%;
        }
    }
}
.gl_title{
    padding: 80px 0;
    font-size: 38px;
    text-align: center;
    font-weight: bold;
    letter-spacing: 2px;
    @media @max-sm {
        padding: 60px 0;
        font-size: 32px;
    }
    @media @max-xs {
        padding: 40px 0;
        font-size: 20px;
        letter-spacing: 1px;
    }
}
.gl_list{
    background: url(../images/bg_03.png) no-repeat right bottom;
    padding-bottom: 200px;
    background-size: auto 240px;
    @media @max-sm {
        padding-bottom: 140px;
        background-size: auto 140px;
    }
    @media @max-xs {
        padding-bottom: 20px;
        background-size: auto 80px;
    }
    li{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 50px;
        @media @max-xs {
            padding-bottom: 70px;
            align-items: flex-start;
        }
        &:nth-child(2n){
            flex-direction: row-reverse;
            .nei{
                padding-left: 0;
                padding-right: 10%;
                text-align: right;
                @media @max-sm {
                    padding-right: 8%;
                }
                @media @max-xs {
                    padding-right: 20px;
                }
            }
        }
    }
    .img{
        width: 48%;
        @media @max-xs {
            width: 40%;
        }
    }
    .nei{
        width: 50%;
        padding-left: 10%;
        @media @max-sm {
            padding-left: 8%;
        }
        @media @max-xs {
            width: 60%;
            padding-left: 20px;
        }
        .title{
            font-size: 36px;
            color: #464646;
            line-height: 50px;
            font-weight: bold;
            @media @max-sm {
                line-height: 46px;
                font-size: 28px;
            }
            @media @max-xs {
                font-size: 20px;
                line-height: 24px;
            }
        }
        .title_en{
            line-height: 40px;
            font-size: 30px;
            color: #333333;
            opacity: 0.1;
            font-weight: bold;
            text-transform: uppercase;
            @media @max-sm {
                line-height: 30px;
                font-size: 22px;white-space: nowrap;
            }
            @media @max-xs {
                font-size: 14px;
                line-height: 20px;
            }
        }
        .desc{
            padding-top: 20px;
            font-size: 22px;
            color: #464646;
            line-height: 40px;
            @media @max-sm {
                font-size: 18px;
                line-height: 32px;
                padding-top: 15px;
            }
            @media @max-xs {
                padding-top: 10px;
                font-size: 14px;
                line-height: 22px;
            }
        }
    }
}
.gl_two{
    background: url(../images/bg_04.jpg) no-repeat top center;
    background-size: cover;
    padding: 160px 0;
    @media @max-sm {
        padding: 100px 0;
    }
    @media @max-xs {
        padding: 40px 0;
    }
    .container{
        display: flex;
        justify-content: space-between;
        align-items: center;
        @media @max-xs {
            flex-wrap: wrap;
        }
    }
    .right_img{
        display: flex;
        flex-shrink: 0;
        padding-left: 50px;
        @media @max-sm {
            padding-left: 30px;
        }
        @media @max-xs {
            width: 100%;
            padding-left: 0px;
            justify-content: center;
            padding: 30px 0 10px 0;
        }
        img{
            height: 200px;
            margin-left: 50px;
            @media @max-sm {
                height: 140px;
                margin-left: 30px;
            }
            @media @max-xs {
                margin: 0 15px;
                height: 100px;
            }
        }
    }
    .nei{
        .title{
            color: #464646;
            font-size: 36px;
            font-weight: 600;
            @media @max-sm {
                font-size: 30px;
            }
            @media @max-xs {
                font-size: 20px;
            }
        }
        .title_en{
            color: #333333;
            opacity: 0.1;
            font-size: 28px;
            font-weight: bold;
            font-weight: 600;
            text-transform: uppercase;
            @media @max-sm {
                font-size: 22px;
            }
            @media @max-xs {
                font-size: 15px;
            }
        }
        .desc{
            font-size: 20px;
            color: #474747;
            line-height: 42px;
            padding-top: 20px;
            @media @max-sm {
                line-height:32px;
                font-size: 16px;
                padding-top: 10px;
            }
            @media @max-xs {
                font-size: 14px;
                line-height: 22px;
            }
        }
    }
}

/* 活动报名 */
.bm_bg{ background: #F7F7F7;}
.bm_banner{
    background: url(../images/bg_05.png) no-repeat center center;
    background-size: cover;
    height: 900px;
    display: flex;
    justify-content: center;
    align-items: center;
    @media @max-sm {
        height: 560px;
        .img{
            height: 70px;
        }
    }
    @media @max-xs {
        height: auto;
        padding: 50px 0;
        .img{
            height: 30px;
        }
    }
    .title2{
        padding: 15px 0 10px 0;
        line-height: 80px;
        color: #101010;
        font-size: 52px;
        font-weight: bold;
        @media @max-sm {
            line-height: 60px;
            font-size: 40px;
        }
        @media @max-xs {
            line-height: 30px;
            font-size: 20px;
        }
    }
    .desc{
        color: #464646;
        font-size: 34px;
        line-height: 40px;
        @media @max-sm {
            font-size: 26px;
            line-height: 32px;
        }
        @media @max-xs {
            font-size: 14px;
            line-height: 22px;
        }
    }
    .more{
        margin-top: 100px;
        display: block;
        width: 360px;
        text-align: center;
        background: @color2;
        line-height: 66px;
        color: #fff;
        font-size: 26px;
        letter-spacing: 2px;
        @media @max-sm {
            margin-top: 80px;
            width: 280px;
            line-height: 56px;
            font-size: 22px;
        }
        @media @max-xs {
            margin-top: 30px;
            width: 160px;
            line-height: 38px;
            font-size: 15px;
        }
    }
}
.bm_one{
    display: flex;
    justify-content: space-between;
    margin: 0 -10px;
    margin-top: -80px;
    @media @max-sm {
        margin: 0 -6px;
        margin-top: -50px;
    }
    @media @max-xs {
        margin-top: -20px;
        flex-wrap: wrap;
    }
    li{
        width: 25%;
        background: #fff;
        border: 1px solid #E9E9E9;
        border-radius: 6px;
        padding: 20px;
        height: 145px;
        text-align: center;
        margin: 10px;
        transition: 0.5s;
        &:hover{
            -moz-box-shadow:0px 0px 10px rgba(0,0,0,0.2);
            -webkit-box-shadow:0px 0px 10px rgba(0,0,0,0.2);
            box-shadow:0px 0px 10px rgba(0,0,0,0.2);
            margin-top: -15px;
        }
        @media @max-sm {
            margin: 0 6px;
            height: 100px;
            padding: 15px 0;
        }
        @media @max-xs {
            margin: 0;
            border-radius: 0;
            width: 50%;
            height: auto;
            margin-top: -1px;
            &:nth-child(2n){
                border-left: 0;
            }
        }
    }
    .img{
        height: 70px;
        @media @max-sm {
            height: 50px;
        }
        @media @max-xs {
            height: 40px;
        }
    }
    .title{
        font-size: 18px;
        color: #727272;
        line-height: 24px;
        padding-top: 12px;
        @media @max-sm {
            padding-top:5px;
            font-size: 15px;
        }
        @media @max-xs {
            font-size: 14px;
        }
    }
}
.bm_title{
    padding: 50px 0;
    text-align: center;
    @media @max-sm {
        padding: 35px 0;
    }
    @media @max-xs {
        padding: 40px 0 20px 0;
    }
    .title{
        font-size: 40px;
        line-height: 60px;
        letter-spacing: 2px;
        font-weight: 600;
        @media @max-sm {
            font-size: 28px;
            line-height: 40px;
        }
        @media @max-xs {
            font-size: 22px;
            line-height: 30px;
        }
    }
    .title_en{
        color: #333333;
        font-size: 30px;
        text-transform: uppercase;
        opacity: 0.1;
        font-weight: 600;
        @media @max-sm {
            font-size: 20px;
        }
        @media @max-xs {
            font-size: 16px;
        }
    }
}
.bm_two{
    background: url(../images/bg_08.jpg) no-repeat;
    background-size: 100% 370px;
    padding-top: 50px;
    @media @max-sm {
        padding-top: 40px;
        background-size: 100% 260px;
    }
    @media @max-xs {
        padding-top: 20px;
        background-size: 100% 130px;
    }
    ul{
        display: flex;
        justify-content: space-between;
        @media @max-xs {
            flex-wrap: wrap;
            padding: 0 9px;
            justify-content: center;
        }
    }
    li{
        width: 32%;
        border-radius: 6px;
        overflow: hidden;
        background: #fff;
        transition: 0.5s;
        &:hover{
            -moz-box-shadow:0px 0px 10px rgba(0,0,0,0.2);
            -webkit-box-shadow:0px 0px 10px rgba(0,0,0,0.2);
            box-shadow:0px 0px 10px rgba(0,0,0,0.2);
        }
        @media @max-xs {
            border-radius:4px;
            width: 47%;
            margin: 0 1.5%;
            margin-bottom: 15px;
        }
    }
    .pimg{
        display: flex;
        justify-content: center;
        align-items:center;
        height: 320px;
        background: #F1F4FD;
        .img{
            max-height: 85%;
            max-width: 85%;
        }
        @media @max-sm {
            height: 220px;
        }
        @media @max-xs {
            height: 110px;
        }
    }
    .bottom{
        display: flex;
        justify-content: center;
        align-items:center;
        height: 110px;
        flex-wrap: wrap;
        padding: 15px 0;
        @media @max-sm {
            height: 80px;
            padding: 10px 0;
        }
        @media @max-xs {
            height: 60px;
            padding: 10px 0;
        }
        .title{
            font-size: 18px;
            width: 100%;
            text-align: center;
            @media @max-sm {
                font-size: 16px;
            }
            @media @max-xs {
                font-size: 14px;
            }
        }
        dl{
            display: flex;
            justify-content: space-between;
            width: 100%;
            padding: 0 15px;
            @media @max-sm {
                padding: 0 10px;
            }
            @media @max-xs {
                padding: 0;
            }
            dd{
                width: 31%;
                text-align: center;
                line-height: 28px;
                border-radius: 40px;
                font-size: 16px;
                @media @max-sm {
                    font-size: 12px;
                    line-height: 22px;
                }
                @media @max-xs {
                    border: 0 !important;
                    font-size: 12px;
                }
                &:nth-child(1){
                    color: #FFAD00;
                    border: 1px solid #FFAD00;
                }
                &:nth-child(2){
                    color: #00CCFF;
                    border: 1px solid #00CCFF;
                }
                &:nth-child(3){
                    color: #0D6BFD;
                    border: 1px solid #0D6BFD;
                }
            }
            
        }
    }
}
.bm_three{
    background: url(../images/bg_09.png) no-repeat left top #fff;
    padding: 40px 0;
    overflow: hidden;
    @media @max-sm {
        background-size: 40%;
    }
    @media @max-xs {
        padding: 20px 0;
        background-size: 40% 100%;
    }
    .container{
        display: flex;
        justify-content: space-between;
        align-items: center;
        @media @max-xs {
            
        }
    }
    .pimg{
        width: 47%;
        img{
            display: block;
            width: 100%;
        }
        @media @max-xs {
            width: 45%;
        }
    }
    .right{
        width: 47%;
        @media @max-xs {
            width: 50%;
        }
        dt{
            line-height: 30px;
            border-left: 12px solid #0D6CFE;
            font-size: 30px;
            padding-left: 15px;
            font-weight: 500;
            @media @max-sm {
                font-size: 22px;
                line-height: 26px;
                border-left: 10px solid #0D6CFE;
            }
            @media @max-xs {
                font-size: 16px;
                line-height: 20px;
                border-left: 5px solid #0D6CFE;
                padding-left: 10px;
                font-weight: bold;
            }
        }
        dd{
            padding: 30px 0;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            @media @max-sm {
                padding: 15px 0;
            }
            @media @max-xs {
                padding: 10px 0;
            }
            span{
                width:45%;
                line-height: 56px;
                background: #E7F0FF;
                border-radius: 40px;
                margin: 15px 0;
                text-align: center;
                font-size: 20px;
                @media @max-sm {
                    margin: 10px 0;
                    line-height: 44px;
                    font-size: 17px;
                    width:47%;
                }
                @media @max-xs {
                    margin: 5px 0;
                    font-size: 14px;
                    line-height: 30px;
                }
            }
        }
    }
}
.bm_four{
    display: flex;
    justify-content: space-between;
    padding-bottom: 80px;
    @media @max-xs {
        padding-bottom: 20px;
    }
    li{
        width: 48%;
        padding-bottom: 20px;
        img{
            display: block;
            width: 100%;
            &:hover{
                -moz-box-shadow:0px 0px 10px rgba(0,0,0,0.3);
                -webkit-box-shadow:0px 0px 10px rgba(0,0,0,0.3);
                box-shadow:0px 0px 10px rgba(0,0,0,0.3);

                -webkit-transform:scale(1.04);
                -moz-transform:scale(1.04);
                transform:scale(1.04);
            }
        }
    }
}
.bm_five{
    padding: 100px 0;
    background: url(../images/bg_10.jpg) no-repeat center center;
    background-size: cover;
    @media @max-sm {
        padding: 70px 0;
    }
    @media @max-xs {
        padding: 20px 0;
    }
    ul{
        display: flex;
        justify-content: space-between;
        @media @max-xs {
            flex-wrap: wrap;
        }
    }
    li{
        width: 30%;
        background: #fff;
        text-align: center;
        padding: 50px 0;
        transition: 0.4s;
        @media @max-sm {
            padding: 30px 0;
            width: 31%;
        }
        @media @max-xs {
            width: 85%;
            display: flex;
            align-items: center;
            padding:  15px 15px;
            margin: 5px 0;
            
            &:nth-child(2){
                margin-left: 7%;
            }
            &:nth-child(3){
                margin-left: 15%;
            }
        }
        &:hover{
            -moz-box-shadow:0px 0px 10px rgba(0,0,0,0.3);
            -webkit-box-shadow:0px 0px 10px rgba(0,0,0,0.3);
            box-shadow:0px 0px 10px rgba(0,0,0,0.3);
            margin-top: -30px;
            
            @media @max-xs {margin-top: 0px;}
        }
    }
    .img{
        height: 180px;
        @media @max-sm {
            height: 110px;
        }
        @media @max-xs {
            flex-shrink: 0;
            width: 70px;
            height: auto;
        }
    }
    .title{
        padding-top: 30px;
        font-size: 17px;
        line-height: 28px;
        @media @max-sm {
            font-size: 14px;
            line-height: 22px;
        }
        @media @max-xs {
            width: 100%;
            padding-top: 0;
            text-align: left;
            padding-left: 15px;
            font-size: 13px;
        }
    }
}
.bm_six{
    margin: 0 -25px;
    display: flex;
    flex-wrap: wrap;
    @media @max-sm {
        margin: 0 -15px;
    }
    @media @max-xs {
        margin: 0 -8px;
        padding-bottom: 20px;
    }
    li{
        width: 33.333%;
        padding: 0 25px 50px 25px;
        @media @max-sm {
            padding: 0 15px 30px 15px;
        }
        @media @max-xs {
            width: 100%;
            padding: 0 8px 15px 8px;
        }
    }
    .bottom{
        display: block;
        width: 100%;
        @media @max-xs {
            padding-left: 15px;
        }
    }
    .nei{
        background: #fff;
        padding: 15px 15px 0 15px;
        border-radius: 6px;
        display: block;
        -moz-box-shadow:0px 0px 10px rgba(0,0,0,0.05);
        -webkit-box-shadow:0px 0px 10px rgba(0,0,0,0.05);
        box-shadow:0px 0px 10px rgba(0,0,0,0.05);
        @media @max-sm {
            padding: 10px 10px 0 10px;
        }
        @media @max-xs {
            padding: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        &:hover{
            -moz-box-shadow:0px 0px 10px rgba(0,0,0,0.3);
            -webkit-box-shadow:0px 0px 10px rgba(0,0,0,0.3);
            box-shadow:0px 0px 10px rgba(0,0,0,0.3);
            .pimg img{
                -webkit-transform:scale(1.05);
                -moz-transform:scale(1.05);
                transform:scale(1.05);
            }
            .title{
                color: @color1;
            }
        }
    }
    .pimg{
        width: 100%;
        display: block;
        height: 240px;
        border-radius: 6px;
        overflow: hidden;
        img{
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        @media @max-sm {
            height: 140px;
        }
        @media @max-xs {
            height: 90px;
            width: 140px;
            flex-shrink: 0;
        }
    }
    .title{
        margin: 17px 0 15px 0;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        font-size: 18px;
        color: #000;
        font-weight: 600;
        transition: 0.4s;
        @media @max-sm {
            font-size: 16px;
            margin: 15px 0 10px 0;
        }
        @media @max-xs {
            margin:0 0 6px 0;
            font-size: 15px;
        }
    }
    .desc{
        font-size: 16px;
        line-height: 24px;
        color: #929292;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        height: 72px;
        @media @max-sm {
            font-size: 14px;
        }
        @media @max-xs {
            font-size: 12px;
            line-height: 20px;
            height: 40px;
            -webkit-line-clamp: 2;
        }
    }
    .more{
        border-top: 1px solid #F3F3F3;
        margin-top: 10px;
        line-height: 56px;
        text-align: center;
        color: @color1;
        @media @max-sm {
            line-height: 44px;
            font-size: 15px;
        }
        @media @max-xs {
            line-height: 40px;
            font-size: 14px;
            display: none;
        }
    }
}

/* 价格 */
.jg_bg{
    // background: linear-gradient(to bottom, #F9FFFF, #E3ECFD);
    background: url(../images/jg_01.png) no-repeat top center #F5F5F7;
    background-size:100%;
}
.jg_one{
    padding: 90px 0 ;
    text-align: center;
    @media @max-sm {
        padding: 70px 0 50px 0;
    }
    @media @max-xs {
        padding: 40px 0 30px 0;
    }
    .title{
        font-size: 38px;
        font-weight: 600;
        line-height: 70px;
        letter-spacing: 2px;
        @media @max-sm {
            font-size: 30px;
            line-height: 60px;
        }
        @media @max-xs {
            letter-spacing: 1px;
            font-size: 20px;
            line-height: 30px;
        }
    }
    .desc{
        font-size: 18px;
        line-height: 32px;
        padding: 0 10%;
        padding-top: 10px;
        @media @max-sm {
            padding: 0;
            padding-top: 10px;
            font-size: 16px;
            line-height: 30px;
        }
        @media @max-xs {
            font-size: 15px;
            line-height: 28px;
        }
    }
}
.jt_two_title{
    text-align: center;
    font-size: 38px;
    font-weight: 600;
    line-height: 110px;
    letter-spacing: 2px;
    @media @max-sm {
        font-size: 30px;
        line-height: 60px;
    }
    @media @max-xs {
        letter-spacing: 1px;
        font-size: 20px;
        line-height: 50px;
    }
}
.jg_two{
    display: flex;
    margin: 0 -10px;
    padding-bottom: 10px;
    @media @max-xs {
        flex-wrap: wrap;
        margin: 0;
    }
    li{
        width: 33.333%;
        padding: 10px 15px;
        @media @max-xs {
            width: 100%;
            padding: 10px 0;
        }
        .nei{
            border-radius: 10px;
            overflow: hidden;
            background: #fff;
            height: 100%;
        }
        .img{
            display: block;
            width: 100%;
        }
    }
    .title{
        text-align: center;
        font-size: 26px;
        font-weight: bold;
        line-height: 40px;
        margin-top: -20px;
        letter-spacing: 2px;
        position: relative;
        .img_max{
            position: absolute;
            left: 64%;
            top: -45px;
            height: 50px;
            @media @max-sm {
                left: 64%;
                top: -35px;
                height: 40px;
            }
            @media @max-xs {
                left: 64%;
                top: -35px;
                height: 50px;
            }
        }
        @media @max-sm {
            font-size: 22px;
            line-height: 34px;
            margin-top: -10px;
        }
        @media @max-xs {
            font-size: 26px;
            line-height: 34px;
            margin-top: -40px;
        }
    }
    .desc{
        padding: 0 45px;
        margin-top: 20px;
        height: 120px;
        line-height: 28px;
        font-size: 16px;
        color: #4E5A6A;
        @media @max-sm {
            padding: 0 20px;
            font-size: 14px;
            line-height: 24px;
        }
        @media @max-xs {
            padding: 0 30px;
            height: auto;
            padding-bottom: 15px;
        }
    }
    .price{
        line-height: 50px;
        color: #000;
        font-size: 20px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        white-space: nowrap;
        @media @max-sm {
            font-size: 18px;
        }
        i{
            color: #000;
            font-size: 20px;
            font-weight: 700;
            margin-right: 10px;
            font-style: normal;
            @media @max-sm {
                font-size: 18px;
            }
            @media @max-xs {
                
            }
        }
        strong{
            font-size: 40px;
            font-weight: 700;
            @media @max-sm {
                font-size: 30px;
            }
            @media @max-xs {
                font-size: 34px;
            }
        }
        
        em{
            background: #FFD24B;
            display: inline-block;
            line-height: 24px;
            border-radius: 5px;
            color: #996622;
            font-size: 14px;
            padding: 0 8px;
            margin-right: 5px;
            font-style: normal;
        }
        span{
            padding-top: 10px;
        }
    }
    .tip{
        font-size: 13px;
        text-align: center;
        line-height: 20px;
        height: 40px;
        color: #707071;
        margin-top: -10px;
        @media @max-xs {
            height: auto;
            padding-bottom: 10px;
        }
    }
    .old_price{text-decoration:line-through}
    .pbtn{
        text-align: center;
        padding-bottom: 30px;
        @media @max-sm {
            padding-bottom: 30px;
        }
    }
    .btn{
        margin: 0 auto;
        width: 210px;
        line-height: 50px;
        display: inline-block;
        text-align: center;
        color: #fff;
        font-size: 16px;
        border-radius: 10px;
        font-weight: bold;
        cursor: pointer;
        letter-spacing: 2px;
        transition: 0.4s;
        @media @max-sm {
            width: 90%;
            line-height: 46px;
            font-size: 18px;
        }
    }
    .bottom{
        padding: 0 45px;
        padding-bottom: 10px;
        @media @max-sm {
            padding: 0 20px;
            padding-bottom: 10px;
        }
        @media @max-xs {
            padding: 0 30px;
            padding-bottom: 10px;
        }
        .tit{
            text-align: left;
            line-height: 62px;
            font-size: 20px;
            //border-top: 1px solid #5187ff;
            @media @max-sm {
                line-height: 30px;
                font-size: 20px;
                padding: 15px 0;
            }
        }
        dd{
            background: url(../images/jg_05.png) no-repeat left 7px;
            padding-left: 25px;
            color: #4E5A6A;
            background-size: 14px;
            margin-bottom: 10px;
            font-size: 15px;
            line-height: 25px;
        }
    }
    
    .li1{
        //.nei{ border: 4px solid #C0EAF4;}
        //.price{color: #47C4DA;}
        .btn{ 
            background: linear-gradient(to right, #8BAEFF, #5488FE);
            &:hover{
                background: linear-gradient(to right, #5488FE, #5488FE);
            }
        }
        /*
        .bottom{
            .tit{border-top: 1px solid #47C4DA}
            dd{
                background: url(../images/bg_11.png) no-repeat left 5px;
                background-size: 14px;
            }
        }
        */
    }
    .li2{
        //.nei{ border: 4px solid #C0D5FE;}
        //.price{color: #5187FF;}
        .btn{ 
            background: linear-gradient(to right, #EDD8C5, #D3B09A);
            color: #533217;
            &:hover{
                background: linear-gradient(to right, #D3B09A, #D3B09A);
            }
        }
        // .bottom{
        //     .tit{border-top: 1px solid #5187ff}
        //     dd{
        //         background: url(../images/bg_11-2.png) no-repeat left 5px;
        //         background-size: 14px;
        //     }
        // }
    }
    .li3{
        //.nei{ border: 4px solid #F9DFB1;}
        //.price{color: #FF9500;}
        .btn{  
            background: linear-gradient(to right, #4B4D59, #252527);
            color: #EECCA9;
            &:hover{
                background: linear-gradient(to right, #252527, #252527);
            }
        }
        // .bottom {
        //     //.tit{border-top: 1px solid #FF9500}
        //     dd{
        //         background: url(../images/bg_11-3.png) no-repeat left 5px;
        //         background-size: 14px;
        //     }
        // }
    }
}
.jg_title{
    text-align: center;
    padding: 40px 0;
    font-size: 40px;
    font-weight: 600;
    letter-spacing: 3px;
    @media @max-sm {
        padding: 30px 0;
        font-size: 30px;
    }
    @media @max-xs {
        font-size: 22px;
        padding: 20px 0;
        letter-spacing:2px;
    }
}
.jg_three{
    width: 100%;
    
    .tb{
    background: #fff;
        width: 100%;
    }
    th{
        background: #fff;
        line-height: 54px;
        font-size: 20px;
        // &.th{
        //     border-left: 2px solid #fff;
        // }
        @media @max-sm {
            line-height: 46px;
            font-size: 18px;
        }
        @media @max-xs {
            line-height: 20px;
            font-size: 15px;
            padding: 10px 5px;
            white-space: nowrap;
        }
    }
    .title{
        padding: 0 !important;
        font-weight: bold;
        font-size: 19px;
        border-bottom: 2px solid #bfc3ca;
        text-align: center;
        span{
            font-size: 14px;
            display: block;
            width: 100%;
        }
        @media @max-sm {
            font-size: 18px;
        }
        @media @max-xs {
            font-size: 15px;
            line-height: 22px;
        }
    }
    .xian{
        border-bottom: 2px solid #bfc3ca;
    }
    tr{
        &:hover{
            background: #E9EFFD;
            .title{background: #F5F5F7;}
        }
    }
    td{
        padding: 10px 0;
        line-height:26px ;
        font-size: 16px;
        border-bottom: 1px solid #e6e6e7;
        min-height: 50px;
        padding-left: 15px;
        @media @max-sm {
            font-size: 16px;
        }
        @media @max-xs {
            font-size: 14px;
            padding: 7px 0 7px 10px;
            line-height: 22px;
        }
    }
    .price{
        font-weight: 100;
        font-size: 15px;
        text-align: center;
        font-weight: bold;
        opacity: 0.9;
        padding: 0;
        @media @max-sm {
            font-size: 15px;
        }
        @media @max-xs {
            font-size: 12px;
            padding: 0 5px;
        }
    }
    .no{
        background: url(../images/tdNoCheck.png) no-repeat center center;
        background-size: 20px;
        @media @max-sm {
            background-size: 14px;
        }
        @media @max-xs {
            background-size: 12px;
        }
    }
    .yes{
        background: url(../images/tdCheck.png) no-repeat center center;
        background-size: 22px;
        @media @max-sm {
            background-size: 22px;
        }
        @media @max-xs {
            background-size: 20px;
        }
    }
    .a_zx{
        background: @color1;
        color: #fff;
        display: inline-block;
        padding: 0 10px;
        border-radius: 3px;
        font-weight: normal;
        &:hover{
            background: @color3;
        }
    }
}
.jg_four{
    //border: 8px solid #BACEFC;
    border-radius: 70px;
    overflow: hidden;
    background: url(../images/jg_06.png) no-repeat left top;
    background-size: 100% 100%;
    display: flex;
    justify-content:center;
    flex-wrap: wrap;
    padding: 40px 0 50px 0;
    align-items: center;
    @media @max-sm {
        border-radius: 30px;
        padding: 30px 0;
    }
    @media @max-xs {
        border-radius: 15px;
        overflow: hidden;
        padding: 20px 0;
        position: relative;
        background: url(../images/jg_07.png) no-repeat left top;
        background-size: 100% 100%;
    }
    
    .ma{
        width: 170px;
        border: 10px solid #fff;
        flex-shrink: 0;
        border-radius: 10px;
        @media @max-sm {
            width: 130px;
        }
        @media @max-xs {
            width: 100px;
            border: 5px solid #fff;
        }
    }
    .txt{
        text-align: center;
        width: 100%;
        font-size: 32px;
        line-height: 56px;
        font-weight: bold;
        letter-spacing: 4px;
        padding-bottom: 20px;
        @media @max-sm {
            font-size: 20px;
            line-height: 38px;
            letter-spacing: 2px;
        }
        @media @max-xs {
            padding-bottom: 10px;
            font-size: 16px;
            line-height: 28px;
            letter-spacing: 1px;
        }
    }
        
}
.jg_table_title{
    background: #fff;
    position:fixed;
    top: 60px;
    z-index: 10;
    -moz-box-shadow:0px 0px 10px rgba(0,0,0,0.2); 
    -webkit-box-shadow:0px 0px 10px rgba(0,0,0,0.2); 
    box-shadow:0px 0px 10px rgba(0,0,0,0.2);
    padding: 13px 0;
    width: 100%;
    @media (max-width: 767px) {/* 手机 */
        display: none !important;
    }
    .container{
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .left{
        width: 25%;
        padding-left: 15px;
        font-size: 20px;
        font-weight: bold;
        margin-left: 18%;
    }
    .list{
        width:45%;
        display: flex;
        justify-content: space-between;
        li{
            text-align: center;
            width: 33.333%;
            line-height: 28px;
            font-size: 16px;
        }
        h2{
            font-size: 18px;
            font-weight: bold;
        }
        a{
            display: inline-block;
            width: 120px;
            line-height: 34px;
            color: #fff;
            border-radius: 6px;
            font-size: 14px;
            margin-top: 3px;
            background: linear-gradient(to right, #8BAEFF, #5488FE);
            &:hover{
                background: linear-gradient(to right, #5488FE, #5488FE);
            }
        }
        li:nth-child(2) a{
            background: linear-gradient(to right, #EDD8C5, #D3B09A);
            color: #533217;
            &:hover{
                background: linear-gradient(to right, #D3B09A, #D3B09A);
            }
        }
        li:nth-child(3) a{
            background: linear-gradient(to right, #4B4D59, #252527);
            color: #EECCA9;
            &:hover{
                background: linear-gradient(to right, #252527, #252527);
            }
        }
    }
}


/* 数智现场 */
.jj_one{
    width:100%; background:#08287D; height:600px; position:relative; overflow:hidden;
    .img_bg{height:100%; width:100%; object-fit: cover;}
    .nei{ position:absolute; top:0; left:0; width:100%; text-align:center;}
    .nei p img{ max-width:80%}
    p{width:100%}
    .p1{ padding:80px 0;}
    .p3{ position:absolute; bottom:-5px; left:0; width:100%; text-align:center}
    .p3 img{ display:inline-block; max-width:100%}
}
.jj_title{ 
    width:100%; text-align:center; padding-top:60px; padding-bottom:45px;
    h2{ width:100%; color:#1D1D1D; font-size:40px; line-height:65px; letter-spacing:4px;}
    p{width:100%; color:#8F8F8F; font-size:18px; padding-top:16px; padding-bottom:30px; background:url(../images/onsite/bg1.png) no-repeat bottom center; letter-spacing:2px;}
}
.jj_two{
    .bt{display: block; width:100%; font-size:30px; color:#005799; padding-bottom:20px;}
    ul{ display:flex;justify-content: space-between; width:100%; padding-bottom:50px;}
    ul img{ max-width:100%}
    .bottom{ padding-bottom:80px;}
    .bottom{ display: block; width:100%; text-align: center;
        img{ width: 70%;}
    }
}
.jj_three{ 
    background:#EEEEEE; width:100%; padding-bottom:30px;
    .list {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        margin: 0 -30px;
    }
    .list li{
        width:33.33%; padding:0px 30px; padding-bottom:44px;
        .nei{ display: block;width:100%; overflow:hidden; border-radius:15px; height:490px; position:relative;}
        .nei .img{object-fit: cover; width:100%; height:100%;}
    }
    .desc{ position:absolute; bottom:0; left:0; width:100%;background-color:rgba(5,63,143,0.95); padding:35px 40px; height:260px; border-radius:15px; text-align:center; color:#fff; transition:0.5s}
    .desc h3{ display: block; width:100%; font-size:28px;}
    .desc p{ display: block;width:100%; font-size:20px; line-height:35px; padding-top:20px; letter-spacing:2px;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 4;-webkit-box-orient: vertical;}
    li:hover .desc{ height:350px;}
    li:hover .desc p{ height:350px;-webkit-line-clamp:7;}
}
.jj_four{ 
    display: block;  width:100%; background:url(../images/onsite/bg2.jpg) no-repeat bottom center #EEEEEE; background-size:100%; padding-bottom:80px; overflow:hidden;
    .img1{ text-align:center;display: block;  width:100%}
    img{ max-width:100%}
    .img2{ text-align:center; padding-top:50px; display: block;  width:100%}
    .shiyong{ display: block; width:100%; text-align:center; padding-top:30px;}
    .shiyong img{ max-width:30%}
}
.jj_five{ 
    li{ display: block;  width:100%; margin-bottom:55px; display: flex; justify-content: space-between;}
    li .img{ width:50%; height:370px; position:relative; z-index:1;object-fit: cover; border-radius:0 200px 200px 0;}
    li .nei{ width:60%; margin-left:-10%; border-radius:200px 0 0 200px; height:370px; background:url(../images/onsite/bg3.jpg) no-repeat left top; background-size:100% 100%; padding-left:15%; display:flex; align-items:center; padding-right:10%;}
    li h2{ display: block; width:100%; color:#1D1D1D; font-size:32px; padding-bottom:30px; font-weight:normal}
    li .desc{ display: block;  width:100%; color:#5F5F5F; font-size:20px; line-height:34px;}
    li .desc p{ padding:8px 0;}
    li:nth-child(2n) {
        flex-direction: row-reverse;
        .img{border-radius:200px 0 0 200px;}
        .nei{border-radius:0 200px 200px 0; background:url(../images/onsite/bg3-2.jpg) repeat-x left top; background-size:100% 100%; margin-left:0; margin-right:-10%; padding-left:0; padding-left:7%; padding-right: 20%;}
    }
}
.jj_six{
    display: block; width: 100%;
    position:relative;object-fit: cover;
    .img{  display: block;  width:100%; height:580px;object-fit: cover;}
    .swiper-button-next,.swiper-button-prev{ width:36px; height:70px}
    .swiper-button-next{ right:-70px; outline:none; background:url(../images/onsite/right.png) no-repeat left center; background-size:100%}
    .swiper-button-prev{ left:-70px; outline:none; background:url(../images/onsite/left.png) no-repeat right center; background-size:100%}
    .desc{ position:absolute; bottom:0; left:0; line-height:60px; background:#031426; color:#fff; font-size:18px; padding:0px 25px; width:100%}
    .swiper-pagination{ position:absolute; bottom:18px; right:10px;}
    .swiper-pagination-bullet{ width:16px; height:16px; border:2px solid #fff; outline:none; background:none; opacity:1; margin:0px 5px;}
    .swiper-pagination-bullet-active{ border:2px solid #3B8CE5; background:#3B8CE5;}
}
.jj_seven{  
    display: block;  width:100%; background:url(../images/onsite/bg4.png) no-repeat bottom center; background-size:cover; text-align:center;
    img{ max-width:80%;}
    .top{  display: block;  width:100%; padding:20px 0;}
    .bottom{  display: block;  width:100%; padding:60px 0 120px 0;}
}
.jj_eight{ 
    padding-bottom:60px; margin-top:-10px;
    display: flex;
    margin: 0 -15px;
    flex-wrap: wrap;
    li{ width:33.33%; padding:15px; display: block;}
    li .pimg{display: block; width:100%; height:290px; overflow:hidden;}
    li .pimg img{ width:100%; height:100%;object-fit: cover; transition:1s}
    li:hover .pimg img{-webkit-transform:scale(1.1);-moz-transform:scale(1.1);transform:scale(1.1);}
    li h3{display: block; width:100%; text-align:center; padding:15px 40px; height:90px; color:#292929; font-size:18px; line-height:30px; font-weight:normal; letter-spacing:2px;}
}
@media screen and (min-width: 768px) and (max-width: 1200px) {/* 平板 */
	.jj_title{ padding-top:70px;}
	.jj_title h2{ font-size:40px; line-height:54px}
	.jj_title p{ font-size:18px; background-size:auto 5px; padding-bottom:25px;}
	.jj_one{ height:400px;}
	.jj_one .p1{ padding:50px 0;}
	.jj_two{ max-width:96%}
	.jj_two .bt{ font-size:24px;}
	.jj_two ul{ padding-bottom:40px;}
	.jj_two .bottom{ padding-bottom:50px;}
    .jj_three .list{margin: 0 -10px;}
	.jj_three .list li{ padding:0px 10px; padding-bottom:30px;}
	.jj_three .list li .nei{ height:350px}
	.jj_three .desc{ height:180px; padding:20px;}
	.jj_three .desc h3{ font-size:20px;}
	.jj_three .desc p{ padding-top:15px; font-size:15px; line-height:26px; letter-spacing:1px;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 4;-webkit-box-orient: vertical;}
	.jj_four img{ max-width:90%}
	.jj_four .img2 img{ margin-left:-80px}
	.jj_four .shiyong{ padding-top:15px;}
	.jj_four .shiyong img{ max-width:35%}
	.jj_five{ padding:0px;}
	.jj_five li .img{ height:240px;}
	.jj_five li .nei{ height:240px;}
	.jj_five li h2{ font-size:26px; padding-bottom:10px;}
	.jj_five li .nei{ padding-right:20px}
	.jj_five li .desc{ font-size:16px; line-height:26px; }
	.jj_six{ width:86%; margin-left:7%}
	.jj_six .swiper-button-next,.jj_six .swiper-button-prev{ width:30px; height:50px}
	.jj_six .swiper-button-next{ right:-50px;}
	.jj_six .swiper-button-prev{ left:-50px;}
	.jj_six .img{ float:left; width:100%; height:420px;}
	.jj_eight li .pimg{ height:200px;}
	.jj_eight li h3{ font-size:18px; line-height:28px; padding:15px 20px; height:70px;}
}
@media (max-width: 767px) {/* 手机 */
	.jj_title{ padding:0px 30px; padding-top:40px; padding-bottom:15px;}
	.jj_title h2{ font-size:24px; line-height:36px; letter-spacing:2px;}
	.jj_title p{ font-size:14px; background-size:auto 5px; padding-bottom:20px; padding-top:8px;letter-spacing:1px;}
	.jj_one{ height:180px;}
	.jj_one .p1{ padding:20px 0; padding-top:30px}
	.jj_two{ max-width:94%}
	.jj_two .bt{ font-size:18px; padding-bottom:15px;}
	.jj_two ul{ padding-bottom:20px;}
	.jj_two .bottom{ padding-bottom:20px;}
	.jj_three{ padding-bottom:0; margin: 0; padding: 0 15px;}
	.jj_three .list{ padding:0px 8px; margin-bottom: -20px;}
	.jj_three .list li{ padding:0px 7px; padding-bottom:20px; width:50%}
	.jj_three .list li .nei{ height:260px; border-radius:10px;}
	.jj_three .desc{ height:160px; padding:15px; border-radius:10px;}
	.jj_three .desc h3{ font-size:16px;}
	.jj_three .desc p{ padding-top:10px; font-size:12px; line-height:20px; letter-spacing:1px;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 5;-webkit-box-orient: vertical;}
	.jj_four{ padding-bottom:20px;}
	.jj_four img{ max-width:90%}
	.jj_four .img2{ padding-top:20px;}
	.jj_four .img2 img{ margin-left:-20px}
	.jj_four .shiyong{ padding-top:15px;}
	.jj_four .shiyong img{ max-width:38%}
	.jj_five{ padding:0px; margin: 0 -15px;}
	.jj_five li{ margin-bottom:20px;}
	.jj_five li .img{ height:160px; width:40%}
	.jj_five li .nei{ height:160px; width:70%}
	.jj_five li h2{ font-size:16px; padding-bottom:5px; font-weight:bold}
	.jj_five li .nei{ padding-right:20px}
	.jj_five li .desc{ font-size:13px; line-height:22px; }
	.jj_five li .desc p{ padding:4px 0;}
	.jj_five li:nth-child(2n) .nei{ padding-left:20px; padding-right:15%}
	.jj_six{ width:82%; margin-left:9%; margin-top:15px;}
	.jj_six .swiper-button-next,.jj_six .swiper-button-prev{ width:15px; height:40px}
	.jj_six .swiper-button-next{ right:-25px;}
	.jj_six .swiper-button-prev{ left:-25px;}
	.jj_six .img{ float:left; width:100%; height:240px;}
	.jj_six .desc{ line-height:40px; font-size:14px; padding:0px 10px;}
	.jj_six .swiper-pagination{ position:absolute; bottom:8px; right:10px;}
	.jj_six .swiper-pagination-bullet{ width:12px; height:12px;}
	.jj_seven .top{ padding:10px 0;}
	.jj_seven .bottom{ padding:10px 0 40px 0;}
	.jj_eight{ padding:0px 8px; margin-top:0; padding-bottom:40px;}
	.jj_eight li{ width:50%; padding:7px;}
	.jj_eight li .pimg{ height:120px;}
	.jj_eight li h3{ font-size:13px; line-height:22px; padding:7px 10px; height:50px;}
}

/* 会展CRM */
.crm_banner{
    height: 540px;
    background: url(../images/img/crm1.jpg) no-repeat top center;
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items:center;
    @media @max-sm {
        height: 400px;
    }
    @media @max-xs {
        height: auto;
        padding: 50px 0;
    }
    .title{
        font-size: 60px;
        font-weight: bold;
        line-height: 90px;
        letter-spacing: 4px;
        @media @max-sm {
            line-height: 60px;
            letter-spacing: 3px;
            font-size: 44px;
        }
        @media @max-xs {
            line-height: 30px;
            letter-spacing: 1px;
            font-size: 24px;
        }
    }
    .title2{
        color: #FF7827;
        line-height: 60px;
        font-size: 34px;
        font-weight: bold;
        @media @max-sm {
            line-height: 50px;
            font-size: 26px;
            padding-top: 5px;
        }
        @media @max-xs {
            line-height: 36px;
            font-size: 18px;
        }
    }
    .desc{
        padding: 10px 0 80px 0;
        font-size: 20px;
        line-height: 34px;
        color: #0B57B6;
        letter-spacing: 1px;
        @media @max-sm {
            font-size: 16px;
            line-height: 28px;
            padding: 5px 0 50px 0;
        }
        @media @max-xs {
            font-size: 14px;
            line-height: 24px;
            padding: 5px 0 30px 0;
        }
    }
    .more{
        display: block;
        width: 290px;
        line-height: 54px;
        color: #fff;
        padding-left:30px;
        background:url(../images/jt1.png) no-repeat 86% center #005EFE;
        background-size: 22px;
        font-size: 22px;
        &:hover{
            background:url(../images/jt1.png) no-repeat 92% center #005EFE;
            background-size: 22px;
        }
        @media @max-sm {
            width: 240px;
            line-height: 48px;
            font-size: 18px;
        }
        @media @max-xs {
            width: 200px;
            line-height: 42px;
            font-size: 16px;
            padding-left:20px;
        }
    }
}
.crm_one{
    padding: 70px 0;
    text-align: center;
    @media @max-sm {
        padding: 50px 0;
    }
    @media @max-xs {
        padding: 30px 0;
    }
    .title{
        line-height: 100px;
        padding-bottom: 5px;
        background: url(../images/bg_13.png) no-repeat bottom center;
        font-size: 50px;
        letter-spacing: 3px;
        //font-weight: 600;
        @media @max-sm {
            line-height: 80px;
            font-size: 36px;
            letter-spacing: 3px;
        }
        @media @max-xs {
            line-height: 60px;
            font-size: 26px;
            background-size: auto 3px;
            letter-spacing: 2px;
        }
    }
    .tit{
        line-height: 60px;
        font-size: 34px;
        padding: 20px 0;
        letter-spacing: 2px;
        @media @max-sm {
            line-height: 40px;
            font-size: 26px;
        }
        @media @max-xs {
            line-height: 30px;
            font-size: 18px;
            padding: 15px 0;
        }
    }
    .desc{
        font-size: 22px;
        line-height: 44px;
        opacity: 0.98;
        letter-spacing:1px;
        @media @max-sm {
            font-size: 18px;
            line-height: 36px;
        }
        @media @max-xs {
            font-size: 15px;
            line-height: 28px;
            opacity: 1;
        }
    }
}
.crm_two{
    @media @max-xs {
        overflow:hidden;
        margin: 0 -15px;
        padding: 0 15px;
    }
    li{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 80px;
        &:nth-child(2n){
            flex-direction: row-reverse;
            .nei{
                padding-left: 0;
                padding-right: 5%;
            }
        }
        @media @max-sm {
            padding-bottom: 60px;
        }
        @media @max-xs {
            flex-wrap: wrap;
            padding-bottom: 30px;
        }
    }
    .pimg{
        width: 57%;
        flex-shrink: 0;
        img{
            width: 100%;
        }
        @media @max-sm {
            width: 45%;
        }
        @media @max-xs {
            width: 100%;
            text-align: center;
            padding-bottom: 20px;
            img{
                width: 80%;
            }
        }
    }
    .nei{
        width: 100%;
        padding-left: 5%;
        .title{
            font-size: 40px;
            line-height: 50px;
            font-weight: 600;
            padding-bottom: 20px;
            @media @max-sm {
                font-size: 26px;
                line-height: 30px;
            }
            @media @max-xs {
                font-size: 20px;
                line-height: 30px;
                padding-bottom: 10px;
            }
        }
        dd{
            padding-left: 25px;
            background: url(../images/jt2.png) no-repeat left 17px;
            background-size: 13px;
            line-height: 40px;
            font-size: 20px;
            @media @max-sm {
                font-size: 16px;
                line-height:32px;
                background: url(../images/jt2.png) no-repeat left 12px;
                padding-left: 20px;
                background-size: 11px;
            }
            @media @max-xs {
                font-size: 15px;
                line-height:30px;
                background: url(../images/jt2.png) no-repeat left 12px;
                padding-left: 20px;
                background-size: 11px;
            }
        }
    }
}
.crm_title{
    padding: 30px 0;
    text-align: center;
    @media @max-xs {
        padding: 30px 0 15px 0;
    }
    h2{
        line-height: 64px;
        padding-bottom: 5px;
        background: url(../images/bg_13.png) no-repeat bottom center;
        font-size: 50px;
        letter-spacing: 4px;
        //font-weight: 600;
        padding: 30px 0;
        @media @max-sm {
            line-height: 50px;
            font-size: 36px;
            letter-spacing: 3px;
            padding: 22px 0;
        }
        @media @max-xs {
            line-height: 40px;
            font-size: 26px;
            background-size: auto 3px;
            letter-spacing: 2px;
            padding: 18px 0;
        }
    }
    p{
        color: #75777C;
        font-size: 22px;
        line-height: 40px;
        letter-spacing: 2px;
        padding: 15px 0;
        @media @max-xs {
            font-size: 16px;
            letter-spacing: 0;
            line-height: 28px;
            padding: 10px 0;
        }
    }
    .tit{
        color: @color1;
        font-size: 34px;
        line-height: 60px;
        @media @max-sm {
            font-size: 28px;
            line-height: 40px;
        }
        @media @max-xs {
            font-size: 20px;
            line-height: 20px;
            padding-bottom: 10px;
        }
    }
}
.crm_three{
    background:url(../images/bg_14.png) no-repeat top center #C9D5F4;
    //background-size: 100%;
    padding: 40px 0;
    @media @max-sm {
        padding-top: 0;
    }
    .banner{
        margin-top: -50px;
        @media @max-xs {
            margin-top: -10px;
        }
    }
    .nei{
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 0 40px;
        padding-bottom: 20px;
        @media @max-sm {
            padding:0 20px;
        }
        @media @max-xs {
            padding: 0;
            //flex-wrap: wrap;
        }
        .left{
            max-width: 65%;
            @media @max-xs {
                max-width: 100%;
                width: 100%;
            }
            .title{
                font-size: 34px;
                letter-spacing: 2px;
                font-weight: bold;
                line-height: 60px;
                @media @max-sm {
                    font-size: 28px;
                    line-height: 50px;
                }
                @media @max-xs {
                    font-size: 22px;
                    line-height: 40px;
                }
            }
        }
        .right{
            padding-left: 80px;
            flex-shrink: 0;
            text-align: center;
            @media @max-xs {
                padding-left: 20px;
            }
            .num{
                font-size: 50px;
                line-height: 60px;
                font-weight: bold;
                @media @max-sm {
                    font-size: 38px;
                    line-height: 50px;
                }
                @media @max-xs {
                    font-size: 30px;
                    line-height: 40px;
                }
            }
        }
        .desc{
            font-size: 22px;
            line-height: 44px;
            @media @max-sm {
                font-size: 18px;
                line-height: 36px;
            }
            @media @max-xs {
                font-size: 15px;
                line-height: 30px;
            }
        }
    }
}
.crm_four{
    background: url(../images/bg_15.png) no-repeat top center;
    background-size: cover;

    .info{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 50px;
        @media @max-xs {
            flex-wrap: wrap;
            padding-bottom: 40px;
        }
        .txt{
            width: 50%;
            @media @max-xs {
                width: 100%;
            }
            .title{
                font-size: 34px;
                line-height: 40px;
                padding-bottom: 20px;
                color: @color1;
                font-weight: bold;
                @media @max-sm {
                    font-size: 28px;
                    line-height: 36px;
                    padding-bottom: 15px;
                }
                @media @max-xs {
                    font-size: 22px;
                    line-height: 36px;
                    padding-bottom: 10px;
                }
            }
            .desc{
                font-size: 20px;
                line-height: 40px;
                text-align: justify;
                @media @max-sm {
                    font-size: 16px;
                    line-height: 32px;
                }
                @media @max-xs {
                    font-size: 15px;
                    line-height: 28px;
                    padding-bottom: 20px;
                }
            }
        }
        .pimg{
            width: 40%;
            padding: 0 30px;
            @media @max-sm {
                padding: 0;
            }
            @media @max-xs {
                width: 100%;
            }
            img{
                max-width: 100%;
            }
        }
        &.info2{
            .txt{
                width: 57%;
                padding-right: 20%;
                @media @max-sm {
                    width: 50%;
                    padding-right: 0%;
                }
                @media @max-xs {
                    width: 100%;
                    padding-top: 20px;
                }
            }
        }
    }

    .list1{
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        li{
            background: url(../images/bg_16.png);
            border-radius: 20px;
            padding: 25px;
            width: 49%;
            margin-bottom: 50px;
            @media @max-xs {
                width: 100%;
                margin-bottom: 15px;
            }
        }
        .title{
            display: flex;
            align-items: center;
            font-weight: bold;
            font-size: 26px;
            letter-spacing: 3px;
            img{
                height: 75px;
                width: 75px;
                border-radius: 100px;
                margin-right: 15px;
            }
            @media @max-sm {
                img{ width: 56px; height: 56px;}
                font-size: 22px;
                letter-spacing: 1px;
            }
            @media @max-xs {
                img{ width: 50px; height: 50px;}
                font-size: 20px;
            }
        }
        .crm_desc{
            padding-top: 20px;
            @media @max-xs {
                padding-top: 10px;
            }
        }
    }

    .list2{
        display: flex;
        justify-content: space-between;
        padding-bottom: 30px;
        @media @max-xs {
            margin-top: -20px;
            flex-wrap: wrap;
            padding-bottom: 30px;
        }
        li{
            width: 25%;
            background: url(../images/bg_16.png);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 50px;
            &:nth-child(2){
                width: 45%;
                @media @max-xs {
                    width: 100%;
                }
            }
            @media @max-sm {
                padding: 20px;
                margin-bottom: 30px;
            }
            @media @max-xs {
                width: 100%;
                margin-bottom: 15px;
            }
        }
        
    }

}
.crm_bg{
    background: #E9EEFB;
    overflow: hidden;
}
.crm_tit{
    font-size: 24px;
    letter-spacing: 2px;
    line-height: 36px;
    padding-bottom: 10px;
    font-weight: bold;
    @media @max-sm {
        font-size: 20px;
        letter-spacing: 1px;
        line-height: 32px;
    }
    @media @max-xs {
        font-size: 19px;
        line-height: 30px;
    }
}
.crm_desc{
    text-align: justify;
    font-size: 18px;
    line-height: 36px;
    @media @max-sm {
        font-size: 16px;
        line-height: 30px;
    }
    @media @max-xs {
        font-size: 15px;
        line-height: 30px;
    }
}
.crm_five{
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px 0 80px 0;
    @media @max-xs {
        padding: 0px 0 30px 0;
    }
    .txt{
        width: 74%;
        .crm_tit,.crm_desc{
            width: 96%;
        }
        .crm_desc{
            padding-bottom: 20px;
            @media @max-xs {
                padding-bottom: 10px;
            }
        }
    }
    .pimg{
        width: 24%;
    }
    img{
        max-width: 100%;
    }
}
.crm_six{
    .pimg{
        padding: 0 30px;
        @media @max-xs {
            padding: 0 10px;
        }
        img{
            display: block;
            width: 100%;
        }
    }
    .nei{
        background: #5679E3;
        border-radius: 25px;
        padding:30px 45px;
        color: #fff;
        @media @max-sm {
            padding:20px 35px;
        }
        @media @max-xs {
            padding:20px 25px;
            border-radius: 15px;
        }
        .title{
            font-size: 30px;
            padding-bottom: 15px;
            line-height: 40px;
            @media @max-sm {
                font-size: 26px;
                padding-bottom: 10px;
            }
            @media @max-xs {
                padding-bottom: 0;
                line-height: 30px;
                font-size: 22px;
            }
        }
    }
    ul{
        display: flex;
        justify-content: space-between;
        margin: 0 -40px;
        @media @max-sm {
            margin: 0 -30px;
        }
        @media @max-xs {
            flex-wrap: wrap;
            margin: 0;
        }
        li{
            width: 33.333%;
            font-size: 18px;
            line-height: 38px;
            padding: 0 40px;
            text-align: justify;
            &:nth-child(2){
                border-left: 1px dashed #fff;
                border-right: 1px dashed #fff;
                @media @max-xs {
                    border: 0;
                    border-top: 1px dashed #fff;
                    border-bottom: 1px dashed #fff;
                }
            }
            @media @max-sm {
                padding: 0 30px;
                font-size: 16px;
                line-height: 30px;
                margin-top: 10px;
            }
            @media @max-xs {
                width: 100%;
                padding:15px 0;
                margin-top: 0;
                font-size: 15px;
            }
        }
    }
}
.crm_seven{
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: space-between;
    padding-bottom: 40px;
    @media @max-sm {
        padding-bottom: 20px;
    }
    @media @max-xs {
        padding-bottom: 10px;
    }
    .top{
        text-align: center;
        padding-bottom: 20px;
        .tit{
            color: @color1;
            font-size: 34px;
            padding-right: 20px;
            font-weight: normal;
            @media @max-xs {
                font-size: 24px;
                padding-right: 10px;
                line-height: 26px;
            }
        }
        @media @max-xs {
            padding-top: 10px;
        }
    }
    .pimg{
        width: 55%;
        @media @max-sm {
            width: 45%;
        }
        @media @max-xs {
            width: 100%;
        }
        img{
            max-width: 100%;
        }
    }
    .right{
        width: 40%;
        @media @max-sm {
            width: 50%;
        }
        @media @max-xs {
            width: 100%;
            margin-top: -20px;
        }
        .crm_tit{
            padding-top: 35px;
            @media @max-xs {
                padding-top: 25px;
            }
        }
    }
}
.crm_eight{
    display: flex;
    justify-content: space-between;
    @media @max-xs {
        flex-wrap: wrap;
    }
    .txt{
        width: 50%;
        @media @max-xs {
            width: 100%;
        }
    }
    .pimg{
        width: 45%;
        @media @max-xs {
            width: 100%;
            padding: 20px 0;
        }
        img{
            max-width: 110%;
            @media @max-xs {
                width: 100%;
            }
        }
    }
}
.crm_nine{
    display: flex;
    justify-content: space-between;
    @media @max-xs {
        flex-wrap: wrap;
    }
    li{
        width: 42%;
        background: #E9EEFB;
        border-radius: 20px;
        padding: 30px;
        &:nth-child(2){
            width: 56%;
        }
        @media @max-xs {
            width: 100% !important;
            margin-bottom: 10px;
        }
    }
}

/* 数字营销 */
.yx_banner{
    background: url(../images/bg_17.jpg) no-repeat top center;
    background-size: cover;
    .title{
        font-size: 44px;
        color: #fff;
        line-height: 70px;
        @media @max-sm {
            line-height: 60px;
            font-size: 44px;
        }
        @media @max-xs {
            line-height: 30px;
            font-size: 24px;
        }
    }
    .title2{
        font-size: 30px;
        color: #03E6EB;
        font-weight:normal;
        @media @max-sm {
            line-height: 50px;
            font-size: 26px;
        }
        @media @max-xs {
            line-height: 36px;
            font-size: 18px;
            padding: 8px 0;
        }
    }
    .desc{
        color: #fff;
        font-size: 16px;
        @media @max-sm {
            font-size: 16px;
            line-height: 28px;
        }
        @media @max-xs {
            font-size: 14px;
            line-height: 24px;
        }
    }
    .more{
        background:url(../images/jt1.png) no-repeat 86% center #EE8045;
        background-size: 22px;
        &:hover{
            background:url(../images/jt1.png) no-repeat 92% center #FF7F00;
            background-size: 22px;
        }
    }
}
.yx_one{
    display: flex;
    justify-content: space-between;
    padding: 60px 0 20px 0;
    @media @max-sm {
        padding: 30px 0 20px 0;
    }
    @media @max-xs {
        padding: 0;
        flex-wrap: wrap;
    }
    li{
        width: 32%;
        background: url(../images/bg_18.png) no-repeat left top;
        background-size: 100% 100%;
        padding: 20px 50px;
        text-align: center;
        border-radius: 10px;
        overflow: hidden;
        -moz-box-shadow:0px 0px 10px rgba(0,105,255,0.2);
        -webkit-box-shadow:0px 0px 10px rgba(0,105,255,0.2);
        box-shadow:0px 0px 10px rgba(0,105,255,0.2);
        transition: 0.3s;
        &:hover{
            background: url(../images/bg_19.png) no-repeat left top;
            background-size: 100% 100%;
            color: #fff;
        }
        @media @max-sm {
            padding: 20px;
        }
        @media @max-xs {
            width: 100%;
            padding: 30px;
            margin-top: 20px;
        }
    }
    .img{
        width: 120px;
        height: 120px;
        border-radius: 100px;
        -moz-box-shadow:0px 5px 10px rgba(0,0,0,0.15);
        -webkit-box-shadow:0px 5px 10px rgba(0,0,0,0.15);
        box-shadow:0px 5px 10px rgba(0,0,0,0.15);
        @media @max-sm {
            width: 80px;
            height: 80px;
        }
        @media @max-xs {
            
        }
    }
    .title{
        line-height: 50px;
        font-weight: bold;
        font-size: 26px;
        padding: 15px 0 10px 0;
        @media @max-sm {
            font-size: 20px;
            line-height: 32px;
        }
        @media @max-xs {
            
        }
    }
    .tit{
        font-size: 22px;
        line-height: 30px;
        @media @max-sm {
            font-size: 18px;
            line-height: 26px;
        }
        @media @max-xs {
            
        }
    }
    .desc{
        font-size: 18px;
        padding-top: 20px;
        line-height:34px;
        @media @max-sm {
            font-size: 15px;
            line-height: 28px;
        }
    }
}
.yx_two{
    padding: 10px 0;
    font-size: 22px;
    line-height: 40px;
    text-align: center;
    letter-spacing: 2px;
    @media @max-sm {
        font-size: 18px;
        line-height: 34px;
        letter-spacing: 1px;
    }
    @media @max-xs {
        font-size: 15px;
        line-height: 30px;
        letter-spacing: 1px;
        padding: 20px 0 0 0;
    }
}
.yx_three{
    background: url(../images/bg_20.png) no-repeat top center;
    background-size: cover;
    padding: 20px 0;
    @media @max-xs {
        padding: 0;
    }
    .crm_two{
        padding: 20px 0;
        @media @max-xs {
            padding-bottom: 0;
        }
        .nei{
            padding-left: 10%;
        }
    }
}
.yx_four{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0 30px 0;
    .left{
        width: 38%;
    }
    .right{
        width: 58%;
    }
    @media @max-xs {
        flex-wrap: wrap;
        .left,.right{ width: 100%;}
        .left{
            margin-bottom: 20px;
        }
    }
}
.yx_bg{ background-color: #EAF0FD;}
.yx_five{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0 50px 0;
    .left{
        width: 42%;
    }
    .right{
        width: 55%;
    }
    @media @max-xs {
        flex-wrap: wrap;
        .left,.right{ width: 100%;}
        .left{
            margin-bottom: 20px;
        }
    }
}
.yx_six{
    padding: 30px 0 100px 0;
    img{ display: block; width: 100%;}
    @media @max-sm {
        padding: 20px 0 60px 0;
        
    }
    @media @max-xs {
        padding: 10px 0 50px 0;
    }
}

/* 搜索列表 */
.bg_bai{ background: #fff;}
.ss_type{
    margin: 30px 0;
    background: #fff;
    padding: 17px 25px 20px 25px;
    @media @max-sm {
        padding: 12px 20px 15px 20px;
    }
    @media @max-xs {
        margin: 20px 0;
        padding: 7px 15px 10px 15px;
    }
    li{
        display: flex;
        padding: 10px 0;
        @media @max-xs {
            padding: 5px 0;
        }
    }
    .tit{
        flex-shrink: 0;
        white-space: nowrap;
        color: #515a6e;
        font-size: 14px;
        line-height: 32px;
        padding-top: 3px;
    }
    dl{
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        dd{
            margin-left: 10px;
            padding: 0 10px;
            color: #515a6e;
            font-size: 14px;
            line-height: 32px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 3px;
            &:hover{
                color: @color1;
            }
            @media @max-xs {
                line-height: 30px;
            }
        }
        .sel{
            background: @color1;
            color: #fff !important;
        }
    }
}
.ss_list{
    display: flex;
    flex-wrap:wrap;
    padding: 10px;
    @media @max-xs {
        padding-bottom: 0;
    }
    li{
        width: 25%;
        padding: 10px;
        @media @max-sm {
            width: 33.33%;
        }
        @media @max-xs {
            padding: 0px;
            width: 100%;
            margin-bottom: 15px;
        }
    }
    .nei{
        display: block;
        border-radius: 5px;
        border: 1px solid #E9E9E9;
        overflow: hidden;
        &:hover{
            border: 1px solid @color1;
            .pimg img{
                -webkit-transform:scale(1.05);
                -moz-transform:scale(1.05);
                transform:scale(1.05);
            }
            .txt{
                .title{
                    color: @color1;
                }
            }
        }
        @media @max-xs {
            display: flex;
        }
    }
    .pimg{
        width: 100%;
        height: 180px;
        overflow: hidden;
        @media @max-sm {
            height: 140px;
        }
        @media @max-xs {
            width: 40%;
            height: 100px;
            flex-wrap: wrap;
            flex-shrink: 0;
        }
        img{
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
    .txt{
        padding: 12px 15px;
        @media @max-xs {
            width: 60%;
            padding: 5px 0 5px 10px;
        }
        .date{
            color: #999;
            font-size: 12px;
            padding-left: 20px;
            background: url(../images/time.png) no-repeat left center;
            background-size: 16px;
            line-height: 20px;
        }
        .wei{
            padding-left: 20px;
            background: url(../images/wei.png) no-repeat left 2px;
            background-size: 16px;
            font-size: 12px;
            line-height: 20px;
            color: #999;
            @media @max-xs {
                overflow:hidden; text-overflow:ellipsis;white-space:nowrap
            }
        }
        .title{
            height: 44px;
            line-height: 20px;
            font-size: 14px;
            color: #000;
            margin: 10px 0 6px 0;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            @media @max-xs {
                margin: 5px 0 0px 0;
                padding-right: 6px;
            }
        }
    }
}

/* 案例 */
.al_bg{ background: #F3F3F3;}
.al_banner{ 
    display:block; width:100%;
    .img{
        display: block;
        width: 100%;
    }
    .nei{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        .zi{
            width: 550px;
            @media @max-sm {
                width: 350px;
            }
            @media @max-xs {
                width: 150px;
            }
            
        }
    }
    .next,.prev{ 
        width:20px; height:32px; position:absolute; top:50%; z-index:100;
        transform: translate(0, -50%); outline:none; cursor:pointer; opacity: 0;
        transition: 0.5s;
        border-radius: 4px;
        overflow: hidden;
    }
    .next{right:20px; background:url(../images/right.png) no-repeat center center; background-size: 100% 100%;}
    .prev{left:20px;background:url(../images/left.png) no-repeat center center;  background-size: 100% 100%;}
    &:hover{
        .next,.prev{
            opacity: 1;
        }
    }
}
.al_tishi{
    font-size: 16px;
    padding: 20px 0 10px 0;
    line-height: 30px;
    color: #515a6e;
    @media @max-sm {
        font-size: 14px;
        line-height: 20px;
    }
}
.al_main{
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    @media @max-xs {
        flex-wrap: wrap;
    }
    .al_left{
        width: 66%;
        @media @max-sm {
            width: 65%;
        }
        @media @max-xs {
            width: 100%;
        }
    }
    .al_right{
        width: 32%;
        //position: sticky;
        //top: 80px;
        @media @max-sm {
            width: 32%;
        }
        @media @max-xs {
            width: 100%;
        }
    }
}
.al_list{
    margin: 0 -10px;
    display: flex;
    flex-wrap: wrap;
    @media @max-sm {
        margin: 0 -7px;
    }
    li{
        padding: 10px;
        width: 33.33%;
        @media @max-sm {
            width: 50%;
            padding: 7px;
        }
    }
    .nei{
        display: block;
        background: #fff;
        border-radius: 5px;
        padding: 10px;
        &:hover{
            -moz-box-shadow:0px 0px 10px rgba(0,0,0,0.2);
            -webkit-box-shadow:0px 0px 10px rgba(0,0,0,0.2);
            box-shadow:0px 0px 10px rgba(0,0,0,0.2);
            //margin-top: -10px;
        }
        @media @max-xs {
            width: 100%;
        }
    }
    .txt{
        width: 100%;
    }
    .pimg{
        display: block;
        width: 100%;
        height: 170px;
        border-radius:5px;
        overflow: hidden;
        @media @max-xs {
            height: 90px;
            flex-shrink: 0;
        }
        img{
            display: block;
            width: 100%;
            height: 100%;
            // object-fit: cover;
        }
    }
    .title{
        margin: 8px 0 4px 0;
        line-height: 32px;
        height: 32px;
        color: #353535;
        font-size: 16px;
        font-weight: bold;
        @media @max-xs {
            margin: 0;
        }
        overflow: hidden;text-overflow: ellipsis;display: -webkit-box;
        -webkit-line-clamp:1;-webkit-box-orient: vertical;
    }
    .desc{
        color: #999999;
        font-size: 13px;
        line-height: 20px;
        overflow: hidden;text-overflow: ellipsis;display: -webkit-box;
        -webkit-line-clamp:4;-webkit-box-orient: vertical;
        height: 80px;
    }
    .type{
        display: flex;
        overflow: hidden;
        height: 30px;
        padding-top: 8px;
        dd{
            white-space: nowrap; 
            flex-shrink: 0;
            line-height: 20px;
            background: #E5E6EB;
            border-radius: 4px;
            padding: 0 8px;
            margin-right: 6px;
            font-size: 12px;
            color: #61616D;
        }
    }
    .more{
        cursor: pointer;
        border-top: 1px solid #EEEEEE;
        color: @color1;
        text-align: center;
        font-size: 14px;
        margin-top: 10px;
        padding-top: 10px;
        line-height: 24px;
        @media @max-xs {
           
        }
    }
}
.al_one{
    display: block;
    width: 100%;
    @media @max-xs {
        margin-top: 30px;
    }
    img{
        display: block;
        width: 100%;
    }
    .btn{
        text-align: center;
        padding: 15px 0;
        a{
            display: inline-block;
            width: 38%;
            line-height: 28px;
            border: 1px solid @color1;
            color: #fff;
            background: @color1;
            font-size: 14px;
            color: #fff;
            margin: 0 2%;
            border-radius: 30px;
            &:hover{
                opacity: 0.8;
            }
        }
        .a2{
            color: @color1;
            background: #fff;
        }
    }
}
.al_two{
    border-bottom: 1px solid #DFDFDF;
    background: url(../images/bg_21.png) no-repeat left center;
    background-size: 4px 15px;
    padding-left: 18px;
    font-size: 20px;
    color: #0348D9;
    line-height: 60px;
    @media @max-xs {
        line-height: 50px;
    }
}
.al_three{
    padding: 15px;
    background: #fff;
    border-radius: 8px;
    li{
        padding: 15px 0;
        @media @max-xs {
            padding: 10px 0;
        }
    }
    .nei{
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        &:hover{
            .txt .tit{
                color: @color1;
            }
            background: #fff;
        }
    }
    .img{
        width: 120px;
        height: 90px;
        border-radius: 8px;
        object-fit: cover;
        flex-shrink: 0;
    }
    .txt{
        padding-left: 10px;
        .tit{
            font-size: 16px;
            font-weight: bold;
            color: #000;
            transition: 0.4s;
            line-height: 26px;
            overflow: hidden;text-overflow: ellipsis;
            display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;
        }
        .desc{
            font-size: 14px;
            overflow: hidden;text-overflow: ellipsis;display: -webkit-box;
            -webkit-line-clamp: 2;-webkit-box-orient: vertical;
            color: #656565;
            line-height: 22px;
            margin-top: 10px;
        }
    }
}

.case_one{
    background: linear-gradient(to bottom, #FFE9CA,#FCF5ED);
    padding: 30px;
    border-radius: 10px;
    margin: 60px 0 80px 0;
    @media @max-sm {
        padding: 20px;
        margin: 40px 0 50px 0;
    }
    @media @max-xs {
        padding: 15px;
        margin: 0 -15px;
        border-radius: 0;
        margin-bottom: 30px;
    }
}
.case_title{
    padding: 20px 0;
    text-align: center;
    @media @max-xs {
        padding: 15px 0;
    }
    h2{
        background:url(../images/bg_26.png) no-repeat center center;
        background-size: auto 46px;
        line-height: 60px;
        font-weight: bold;
        font-size: 36px;
        letter-spacing: 3px;
        @media @max-sm {
            line-height: 50px;
            background-size: auto 40px;
            font-size: 30px;
        }
        @media @max-xs {
            line-height: 40px;
            background-size: auto 32px;
            font-size: 24px;
        }
    }
    p{
        color: #BD9159;
        font-size: 14px;
        line-height: 28px;
    }
}
.case_two{
    width:100%;
    height: 160px;
    flex-shrink: 0;
    border-radius: 10px;
    position: relative;
    background: #E1E6F1;
    margin-top: 80px;
    padding: 0 40px;
    display: flex;
    justify-content: space-between;
    align-items:center;
    @media @max-sm {
        height: 140px;
        padding: 0 30px;
        margin-top: 50px;
    }
    @media @max-xs {
        display: none;
    }
    .img{
        display: block;
        width: 36%;
        position: absolute;
        bottom: 0;
        right: 40px;
        @media @max-sm {
            width: 40%;
            right: 20px;
        }
        @media @max-xs {
            
        }
    }
    .txt{
        border-radius: 8px;
        @media @max-sm {
            bottom: 12px;
            padding:15px 0px;
        }
        @media @max-xs {
            
        }
        dt{
            line-height: 40px;
            font-size: 24px;
            color: #1D2129;
            font-weight: bold;
            @media @max-sm {
                line-height: 30px;
                font-size: 20px;
            }
            @media @max-xs {
                
            }
        }
        dd{
            line-height: 30px;
            font-size: 14px;
            color: #4E5969;
            @media @max-sm {
                line-height: 26px;
                font-size: 13px;
            }
            @media @max-xs {
                
            }
        }
        .bottom{
            display: flex;
            padding-top: 15px;
            @media @max-sm {
                padding-top: 10px;
            }
            @media @max-xs {
                
            }
            a{
                display: inline-block;
                border: 1px solid @color1;
                width: 104px;
                border-radius: 8px;
                margin-right: 15px;
                line-height: 36px;
                text-align:center;
                color: @color1;
                font-size: 16px;
                &:hover{
                    background: @color1;
                    color:#fff;
                }
                @media @max-sm {
                    width: 90px;
                    border-radius: 6px;
                    font-size: 15px;
                    line-height: 32px;
                }
                @media @max-xs {
                    
                }
            }
            .a1{
                background: @color1;
                color:#fff;
                &:hover{
                    background: @color3;
                    color:#fff;
                }
            }
        }
    }
}
.case_three{
    display: block;
    bottom: 20px;
    background:url(../images/bg_27.png) no-repeat right top #E1E6F1;
    background-size: auto 100%;
    width: 100%;
    padding:20px 30px;
    border-radius: 8px;
    margin-top: 100px;
    margin-bottom: 40px;
    @media @max-sm {
        bottom: 12px;
        padding:15px 20px;
    }
    @media @max-xs {
        margin-top: 40px;
        margin-bottom: 40px;
    }
    dt{
        line-height: 40px;
        font-size: 24px;
        color: #1D2129;
        font-weight: bold;
        @media @max-sm {
            line-height: 30px;
            font-size: 20px;
        }
        @media @max-xs {
            
        }
    }
    dd{
        line-height: 30px;
        font-size: 14px;
        color: #4E5969;
        @media @max-sm {
            line-height: 26px;
            font-size: 13px;
        }
    }
    .bottom{
        display: flex;
        padding-top: 15px;
        @media @max-sm {
            padding-top: 10px;
        }
        @media @max-xs {
            
        }
        a{
            display: inline-block;
            border: 1px solid @color1;
            width: 104px;
            border-radius: 8px;
            margin-right: 15px;
            line-height: 36px;
            text-align:center;
            color: @color1;
            font-size: 16px;
            background: #fff;
            &:hover{
                background: @color1;
                color:#fff;
            }
            @media @max-sm {
                width: 90px;
                border-radius: 6px;
                font-size: 15px;
                line-height: 32px;
            }
            @media @max-xs {
                
            }
        }
        .a1{
            background: @color1;
            color:#fff;
            &:hover{
                background: @color3;
                color:#fff;
            }
        }
    }
}



/* 服务商 */
.fw_main{
    padding: 30px 0;
    .fw_left{
        //width: 60%;
        width: 100%;
    }
    @media @max-xs {
        padding-bottom: 0;
    }
}
.fw_title{
    text-align: center;
    padding-bottom: 20px;
    margin-top: -20px;
    @media @max-xs {
        margin-top: -10px;
    }
    h2{
        line-height: 60px;
        font-size: 32px;
        font-weight: bold;
        color: #1D2129;
        letter-spacing: 3px;
        @media @max-sm {
            font-size: 28px;
            line-height: 50px;
        }
        @media @max-xs {
            font-size: 22px;
            line-height: 40px;
        }
    }
    p{
        font-size: 14px;
        color: #4E5969;
        line-height: 30px;
        @media @max-xs {
            line-height: 20px;
        }
    }
}
.fw_list{
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    li{
        width: 49%;
        padding-bottom: 20px;
        @media @max-xs {
            padding-bottom: 15px;
            width: 100%;
        }
    }
    .nei{
        display: block;
        width: 100%;
        padding: 18px;
        background: #fff;
        border-radius: 6px;
        display: flex;
        justify-content: space-between;
        align-items:flex-start;
        position: relative;
        &:hover{
            -moz-box-shadow:0px 0px 10px rgba(0,0,0,0.2);
            -webkit-box-shadow:0px 0px 10px rgba(0,0,0,0.2);
            box-shadow:0px 0px 10px rgba(0,0,0,0.2);
            //margin-left: 5px;
            .more{
                background: @color1;
                color: #fff;
            }
        }
        @media @max-xs {
            border-radius: 0;
        }
    }
    .plogo{
        width: 80px;
        height: 80px;
        flex-shrink: 0;
        display: flex;
        justify-content:center;
        align-items: center;
        //border: 1px solid #C4C4C4;
        border-radius: 8px;
        overflow: hidden;
        img{
            max-width: 100%;
            max-height: 100%;
        }
        @media @max-xs {
            width: 100px;
            height: 100px;
        }
    }
    .txt{
        width: 100%;
        padding: 0 20px;
        @media @max-xs {
            padding: 0 0 0 15px;
        }
        .name{
            font-size: 18px;
            font-weight: 700;
            line-height: 30px;
            margin-bottom: 10px;
            //color: #0347D8;
            color: #000;
            @media @max-xs {
                font-size: 16px;
                margin-bottom: 5px;
            }
        }
        .type{
            color: #000;
            font-size: 15px;
            line-height: 22px;
            overflow: hidden;text-overflow: ellipsis;
            display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;
            //background:url(../images/bg_22.png) no-repeat left 2px;
            //background-size: 20px;
            //padding-left: 30px;
        }
        .desc{
            font-size: 14px;
            color: #656565;
            line-height: 22px;
            overflow: hidden;text-overflow: ellipsis;
            display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;
            margin-top: 5px;
            @media @max-xs {
                font-size: 12px;
                line-height: 20px;
            }
        }
    }
    .more{
        display: block;
        //background: #EDF7FF;
        color: @color1;
        flex-shrink: 0;
        text-align: center;
        font-size: 15px;
        transition: 0.2s;
        //width: 100px;
        //border-radius: 40px;
        // line-height: 40px;
        width: 120px;
        height: 32px;
        line-height: 30px;
        border: 1px solid @color1;
        border-radius: 5px;
        margin-top: 10px;
        @media @max-sm {
            display: none;
        }
        @media @max-xs {
            
        }
    }
}
.fw_right{
    background: #E1E6F1;
    border-radius: 20px;
    width: 100%;
    margin-top: 20px;
    padding:40px 40px 0 40px;
    .di_img{
        display: block;
        width: 100%;
        border-radius: 8px;
    }
    @media @max-sm {
        position: relative;
        top: 0;
    }
    @media @max-xs {
        width: 100%;
        padding:15px;
        border-radius: 0;
        padding-bottom: 40px;
    }
    .tit{
        padding-bottom: 40px;
        font-size: 34px;
        font-weight: bold;
        text-align: center;
        letter-spacing: 3px;
        line-height: 50px;
        @media @max-sm {
            font-size: 26px;
        }
        @media @max-xs {
            font-size: 22px;
            line-height: 50px;
            padding-bottom: 15px;
            letter-spacing: 3px;
        }
    }
}
.fw_info{
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    align-items: flex-start;
    @media @max-sm {
        flex-wrap: wrap;
    }
    @media @max-xs {
        
    }
    .right_img{
        border-radius: 8px;
        width: 65%;
        img{
            display:block;
            width: 100%;
            border-radius: 12px;
            margin-bottom: 40px;
        }
        @media @max-sm {
            width: 100%;
            img{
                border-radius: 8px;
                margin-bottom: 25px;
            }
        }
        @media @max-xs {
            img{
                border-radius: 8px;
                margin-bottom: 15px;
            }
        }
    }
    .nei{
        width: 32%;
        background: #fff;
        background-size: 100%;
        border-radius: 10px;
        padding: 15px 25px;
        margin-right: 10px;
        margin-bottom: 40px;
        @media @max-sm {
            width: 100%;
            margin-right: 0;
            margin-bottom: 25px;
        }
        @media @max-xs {
            margin-bottom: 0;
            padding: 15px;
        }
        .title{
            line-height: 44px;
            color: #000;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
        }
        h3{
            font-size: 15px;
            line-height: 36px;
            i{
                padding-right: 5px;
                font-style: normal;
                color: red;
            }
        }
        .txt{
            width: 100%;
            display: block;
            border: 1px solid #D7DDFF;
            height: 38px;
            line-height: 36px;
            padding-left: 10px;
            font-size: 15px;
            color: #000;
            border-radius: 4px;
            margin-bottom: 6px;
        }
        .btn{
            display: block;
            background: @color1;
            color: #fff;
            font-size: 16px;
            margin-top: 20px;
            line-height: 40px;
            width: 100%;
            margin-bottom: 5px;
            border-radius: 8px;
            &:hover{
                background: @color3;
            }
        }
    }
}
.fw_banner{
    padding: 70px 0;
    .pc_img{
        display: block;
        width: 100%;
        border-radius: 20px;
    }
    .yd_img{
        display:none;
        width: 100%;
        border-radius: 10px;
    }
    @media @max-sm {
        padding: 40px 0;
    }
    @media @max-xs {
        padding: 15px 0;
        .pc_img{ display: none;}
        .yd_img{ display: block;}
    }
}
/* 详情 */
.xq_info{
    background: #fff;
    margin: 60px 0px 40px 0;
    display: flex;
    align-items: flex-start;
    @media @max-xs {
        margin:0 -15px;
        flex-wrap: wrap;
        padding:20px  15px;
    }
    .pimg{
        width: 420px;
        height:280px;
        flex-shrink: 0;
        @media @max-sm {
            width: 320px;
            height:220px;
        }
        @media @max-xs {
            width: 100%;
        }
        img{
            width: 100%;
            height: 100%;
            object-fit: cover;
            flex-shrink: 0;
            width: 100%;
            height: 100%;
            border-radius: 8px;
            margin-top: -40px;
            margin-left: -20px;
            @media @max-xs {
                margin: 0;
            }
        }
    }
    .nei{
        width: 100%;
        padding: 30px 30px 30px 10px;
        @media @max-xs {
            padding: 15px 0 0 0;
        }
        .title{
            font-size: 22px;
            line-height: 30px;
            padding: 10px 0;
            color: #3A4150;
            font-weight: bold;
            letter-spacing: 1px;
            @media @max-xs {
                font-size: 18px;
                line-height: 28px;
            }
        }
        dd{
            padding: 5px 0;
            line-height: 24px;
            font-size: 15px;
            opacity: 0.8;
            color: #000;
            @media @max-xs {
                font-size: 13px;
                line-height: 22px;
                padding: 5px 0;
            }
        }
        .user{
            padding: 10px 0;
            display: flex;
            align-items: center;
            img{
                width: 36px;
                height: 36px;
                object-fit: cover;
                border-radius: 40px;
                margin-right: 10px;
            }
            span{
                font-weight: bold;
                color: #000;
                opacity: 0.5;
                font-size: 15px;
            }
        }
    }
    
    .bottom{
        display: flex;
        flex-direction: row-reverse;
        align-items: center;
        font-size: 14px;
        width: 160%;
        margin-left: -60%;
        @media @max-xs {
            width: 100%;
            margin: 0;
            padding: 10px 0;
            justify-content: space-between;
        }
        .a_bm{
            padding: 0 15px;
            line-height: 34px;
            border-radius: 3px;
            background: @color1;
            color: #fff;
            font-size: 14px;
            margin-right: 20px;
            &:hover{
                opacity: 0.8;
            }
        }
    }
}

.xq_desc{
    background: #fff;
    padding: 40px 30px;
    margin-bottom: 60px;
    @media @max-xs {
        margin: 0 -15px;
        margin-top: 20px;
        margin-bottom: 20px;
        padding: 25px 15px;
    }
    .title{
        font-size: 20px;
        font-weight: bold;
        padding-bottom: 20px;
        @media @max-xs {
            font-size: 18px;
            padding-bottom: 15px;
        }
    }
    .desc{
        font-size: 16px;
        line-height: 36px;
        color: #000;
        opacity: 0.8;
        padding: 0 30px;
        @media @max-xs {
            padding: 0 ;
            font-size: 14px;
            line-height: 30px;
        }
    }
}
  
/* 分页 */
.fenye {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30px 0 60px 0;
    @media @max-sm {
        padding: 20px 0 40px 0;
    }
    @media @max-xs {
        padding: 15px 0 25px 0;
        a {display: none;}
        .prev,.next {display: block;}
    }
    a,span {
        border: 1px solid #DCDCDC;
        background: #fff;
        line-height: 32px;
        color: #333;
        padding: 0 12px;
        margin: 0px 4px;
        font-size: 14px;
        @media @max-sm {
            font-size: 14px;
            margin: 0 2px;
            line-height: 30px;
            padding: 0 12px;
        }
    }
    span{
        border: 0;
    }
    a:hover{
        color: @color1;
    }
    .sel {
        background: @color1;
        color: #fff !important;
        border: 1px solid @color1;
    }
}


/* 右侧浮框 */
.fu{
    position: fixed;
    top: 50%;
    right:20px;
    width: 46px;
    z-index: 10;
    background: #fff;
    -moz-box-shadow:0px 0px 10px rgba(0,0,0,0.2);
    -webkit-box-shadow:0px 0px 10px rgba(0,0,0,0.2);
    box-shadow:0px 0px 10px rgba(0,0,0,0.2);
    border-radius: 5px;
    @media @max-xs {
        display: none;
    }
    li{
        height: 46px;
        position: relative;
        transition: 0.5s;
        border-radius: 100px;
        &:hover{
            .spanTell{
                opacity: 1;
            }
        }
    }
    i,a{
        display: inline-block;
        width:46px;
        height: 100%;
        cursor: pointer;
    }
    .i_wx{
        background: url(../images/f1.png) no-repeat center center;
        background-size: 30px;
    }
    .i_tell{
        background: url(../images/f2.png) no-repeat center center;
        background-size: 30px;
    }
    .spanTell{
        opacity: 0;
        line-height: 42px;
        color: #000;
        padding-left: 10px;
        cursor: default;
        font-size: 15px;
    }
    #go-top i{
        cursor: pointer;
        background: url(../images/f3.png) no-repeat center center;
        background-size: 30px;
    }
    li:hover .span_wx{
        display:block;
    }
    .span_wx{
        position: absolute;
        top: 0;
        right: 60px;
        display: none;
        transition: 0.5s;
        img{
            width: 120px;
            height: 120px;
            background: #fff;
            padding:5px
        }
    }
    .li:hover{
        width: 180px;
        margin-left: -140px;
        display: flex;
        background-color:#fff;
        .spanTell{
            opacity: 1;
            width: 80%;
        }
    }
}

/* 底部首页悬浮按钮 */
.ind_btn{
    background-color: #fff;
    height: 70px;
    display: none;
    @media @max-xs {
        display: block;
    }
    p{
        display: flex;
        padding: 10px 15px;
        position: fixed;
        bottom: 0;
        left: 0;
        z-index: 100;
        background: #fff;
        height: 70px;
        width: 100%;
        align-items: flex-start;
        -moz-box-shadow:0px 0px 10px rgba(0,0,0,0.2);
        -webkit-box-shadow:0px 0px 10px rgba(0,0,0,0.2);
        box-shadow:0px 0px 10px rgba(0,0,0,0.2);
    }
    a{
        display: inline-block;
        line-height: 40px;
        text-align: center;
        border: 1px solid @color1;
        width: 30%;
        font-size: 16px;
        color: @color1;
        border-radius: 6px;
    }
    .a2{
        width: 70%;
        margin-left: 15px;
        color: #fff;
        background: @color1;
    }
}

/* 试用弹框 */
.tan_shiyong{
    background:url(../images/bg_31.png) repeat-x left top #fff;
    width: 100%;
    max-width: 720px;
    max-height: 95vh;
    overflow-y: auto;
    padding: 40px 40px 20px 40px;
    border-radius: 10px;
    &::-webkit-scrollbar {width: 6px;}
    &::-webkit-scrollbar-track-piece  { width:6px; background-color: #DDDDDD; }
    &::-webkit-scrollbar-thumb{background-color: @color1;}
    box-sizing: border-box;
    @media @max-xs {
        padding:15px;
        max-height: 86vh;
    }

    .tip{
        font-size: 12px;
        color: #4E5969;
        line-height: 18px;
        padding-bottom: 20px;
    }
    .top{
        text-align: left;
        font-size: 16px;
        padding-bottom: 15px;
        font-weight: bold;
        strong{
            color: @color1;
        }
    }
    .tb{
        width: 100%;
        .tit{
            font-weight: normal;
            vertical-align: top;
            padding-right: 10px;
            font-size: 14px;
            i{
                font-style: normal;
                color: red;
                padding-right: 5px;
            }
        }
        th,td{
            padding-bottom: 15px;
            line-height: 34px;
        }
        .txt,.select{
            width: 100%;
            height: 34px;
            line-height: 32px;
            border: 1px solid #C9CDD4 !important;
            padding-left: 10px;
            font-size: 14px;
            border-radius: 4px;
            background: #fff;
        }
        .select_city{
            display: flex;
            justify-content: space-between;
        }
        .txt::-webkit-input-placeholder {color:#C9CDD4;}
        .txt:-moz-placeholder {color:#C9CDD4;}
        .txt::-moz-placeholder {color:#C9CDD4;}
        .txt:-ms-input-placeholder {color:#C9CDD4}  
        .pl20{
            padding-left: 20px;
            @media @max-xs {
                padding-left: 10px;
            }
        }
        .txt_desc{
            padding: 5px 10px;
            line-height: 26px;
            height: 100px;
        }
        .select{
            width: 48.5%;
            padding-left: 5px;
        }
        .list{
            padding: -5px 0 15px 0;
            display: flex;
            flex-wrap: wrap;
        }
        label{
            width: 33.33%;
            font-size: 14px;
            display: flex;
            cursor: pointer;
            align-items: center;
            line-height: 30px;
            input{
                width: 16px;
                height: 16px;
                margin-right: 3px;
            }
            @media @max-xs {
                width: 100%;
            }
        }
        .list1{
            justify-content: flex-start;
            flex-wrap: nowrap;
            label{
                width:auto;
                padding-right: 20px;
            }
            @media @max-xs {
                flex-wrap: wrap;
                
                label{
                    padding: 0;
                    width: 50%;
                }
            }
        }
        .td_btn{
            text-align: right;
        }
        .btn{
            padding: 0 20px;
            line-height: 32px;
            border: 1px solid #E0E0E6;
            color: #000;
            font-size: 14px;
            cursor: pointer;
            margin-left: 10px;
            border-radius: 3px;
            transition: 0.5s;
            &:hover{
                border: 1px solid @color1;
                color: @color1;
            }
        }
        .btn2{
            background: @color1;
            color: #fff !important;
            border: 1px solid @color1;
            &:hover{
                background: @color3;
            }
        }
    }
}

/* 服务商详情 */
.fwxq_banner{
    background: url(../images/jg_01.png) no-repeat top center;
    background-size: 100% 320px;
    height: 320px;
    display: block;
    width: 100%;
    line-height: 250px;
    text-align: center;
    font-weight: 600;
    font-size: 36px;
    color: #1D2129;
}
.fw_one{
    border-radius: 8px;
    background-color: #fff;
    padding: 16px;
    margin-top: 20px;
    margin-bottom: 10px;
    padding:25px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-top: -60px;
    @media @max-xs {
        position: relative;
        padding: 15px;
        flex-wrap: wrap;
    }
    .left_img{
        width: 100px;
        height: 100px;
        border-radius: 5px;
        overflow: hidden;
        //border: 1px solid #E5E7EB;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        img{
            max-width: 100% ;
            max-height: 100%;
        }
        @media @max-xs {
            position:relative;
            width: 100%;
            top: 0;
            left: 0;
        }
    }
    .nei{
        width: 100%;
        padding-left: 20px;
        @media @max-xs {
            padding-left: 0px;
        }
    }
    .title{
        font-weight: bold;
        line-height: 40px;
        font-size: 24px;
        padding-bottom: 5px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        @media @max-xs {
            padding-left: 0;
            flex-wrap: wrap;
            font-size: 18px;
            line-height:26px;
            justify-content: center;
            display: flex;
            flex-wrap: wrap;
            span{ width: 100%; text-align: center;}
        }
        .wei{
            padding-left: 25px;
            background: url(../images/bg_01.png) no-repeat left center;
            background-size: 12px;
            padding-left: 22px;
            font-size: 14px;
            font-weight: normal;
            @media @max-xs {
                width: auto;
                margin-top: 5px;
            }
        }
    }
    .txt{
        display: flex;
        justify-content: space-between;
        align-items: center;
        dd{
            padding-left: 28px;
            font-size: 14px;
            line-height: 28px;
            color: #515A6E;
        }
        .tit{
            //background: url(../images/bg_22.png) no-repeat left center;
            //background-size: 18px;
            padding-left: 0;
            color: #000;
        }
        @media @max-xs {
            padding-left: 0;
            .wei{ display: none;}
        }
    }
    .desc{
        padding: 5px 0 20px 0;
        font-size: 14px;
        line-height: 24px;
        color: #515A6E;
        @media @max-xs {
            padding-left: 0;
            padding-top: 10px;
        }
    }
    .a_zixun{
        line-height: 32px;
        //text-align: center;
        display: inline-block;
        //width: 200px;
        color: @color1;
        border-radius: 4px;
        background:url(../images/bg_25.png) no-repeat 10px center #E8EDF5;
        font-size: 14px;
        padding-left: 33px;
        background-size: 14px;
        color: @color1;
        padding-right: 15px;
        //&:hover{
            //background: @color1;
            //color: #fff;
        //}
    }
}
.layui-layer-tips .layui-layer-content{
    font-size: 16px !important;
    padding: 5px 10px !important;
}
.fw_two{
    border-radius: 8px;
    margin-bottom: 20px;
    position:relative;
    padding-top: 30px;
    /*
    &::before,&::after{
        content: '';
        position: absolute;
        z-index: 10;
        width: 12px;
        height: 37px;
        top: -23px;
        background: url(../images/bg_24.png) no-repeat left top;
        background-size: 12px 37px;
    }
    &::before{
        left: 10px;
    }
    &::after{
        right: 10px;
    }*/
    
    @media @max-sm {
        padding-top: 30px;
    }
    @media @max-xs {
        padding-top: 20px;
    }
    .top{
       // background: url(../images/bg_21.png) no-repeat left center;
        background-size: 4px 16px;
        padding-left: 15px;
        line-height: 90px;
        text-align: center;
        font-size: 32px;
        margin-left: 15px;
        color: #000;
        font-weight: bold;
        letter-spacing: 3px;
        @media @max-sm {
            font-size: 28px;
            line-height: 70px;
        }
        @media @max-xs {
            font-size: 22px;
            line-height: 50px;
        }
    }
    .list{
        display: flex;
        flex-wrap: wrap;
        margin: 0 -15px;
        @media @max-xs {
            margin: 0;
            justify-content: space-between;
        }
        li{
            padding: 15px;
            width: 33.333%;
            @media @max-xs {
                width: 48.5%;
                padding:5px 0;
            }
        }
        .nei{
            display: block;
            height: 100%;
            background: #fff;
            border-radius: 8px;
            padding: 10px;
            @media @max-xs {
                padding: 8px;
                // display: flex;
                // justify-content: space-between;
                // border: 0;
                //border-bottom: 1px solid #E5E7EB;
                //padding: 10px;
            }
        }
        .pimg{
            width: 100%;
            overflow: hidden;
            height: 180px;
            border-radius: 8px;
            img{
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
            
            @media @max-xs {
                flex-shrink: 0;
                width: 100%;
                height: 88px;
                border: 1px solid #E5E7EB;
                border-radius: 4px;
            }
        }
        .bottom{
            padding: 10px 0;
            @media @max-xs {
                padding: 0;
                //padding-left: 10px;
            }
            .title{
                font-size: 16px;
                text-align: left;
                font-weight: bold;
                line-height: 30px;
                color: #000;
                overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;
                @media @max-xs {
                    text-align: left;
                    line-height: 28px;
                    color: #000;
                    margin-top: 5px;
                    font-size: 15px;
                }
            }
            .desc{
                line-height: 22px;
                font-size: 14px;
                overflow: hidden;text-overflow: ellipsis;display: -webkit-box;
                -webkit-line-clamp: 3;-webkit-box-orient: vertical;
                color: #515a6e;
                padding: 4px 0;
                @media @max-xs {
                    padding: 3px 0;
                    -webkit-line-clamp: 2;
                }
            }
        }
    }
}

/* 报名管理 / 营销 */
.bao_banner{
    .container{
        padding: 40px 0;
        @media @max-xs {
            padding: 10px 15px;
        }
    }
    .nei{
        .desc{
            font-size: 22px;
            padding-bottom: 40px;
            @media @max-sm {
                padding-bottom: 30px;
                font-size: 20px;
            }
            @media @max-xs {
                font-size: 14px;
                padding-bottom: 15px;
            }
        }
        .title{
            @media @max-sm {
                width: 130%;
                padding-bottom: 5px;
            }
            @media @max-xs {
                width: 100%;
            }
        }
        .tags{
            display:flex;
            align-items: center;
            margin-top: -27px;
            padding-bottom: 35px;
            flex-wrap: wrap;
            @media @max-sm {
                margin-top: -25px;
                padding-bottom: 20px;
                flex-wrap: wrap;
                width: 120%;
            }
            @media @max-xs {
                margin-top: -10px;
                padding-bottom: 15px;
                display: none;
            }
            dd{
                background: #B9D1FF;
                border-radius: 4px;
                line-height: 32px;
                margin-right: 5px;
                margin-bottom: 5px;
                padding: 0 12px;
                font-size: 14px;
                @media @max-sm {
                    margin-right: 4px;
                    margin-bottom: 4px;
                }
                @media @max-xs {
                    line-height: 22px;
                    padding: 0 8px; font-size: 12px;
                }
            }
        }
    }
}
.bao_one{
    padding: 80px 0 40px 0;
    display: flex;
    margin: 0 -5px;
    @media @max-xs {
        flex-wrap:wrap;
        padding: 20px 0;
        margin: 0 -5px;
        margin-bottom: -5px;
    }
    li{
        width: 33.333%;
        padding: 0 12px;
        @media @max-xs {
            padding: 5px;
        }
    }
    .nei{
        background: #fff;
        display: block;
        border-radius: 10px;
        padding: 30px 0;
        -moz-box-shadow:0px 15px 32px rgba(0,0,0,0.06);
        -webkit-box-shadow:0px 15px 32px rgba(0,0,0,0.06);
        box-shadow:0px 15px 32px rgba(0,0,0,0.06);
        transition: 0.4s;
        &:hover{
            margin-top: -20px;
        }
        @media @max-xs {
            padding: 15px 0 10px 0;
        }
    }
    .pimg{
        padding: 10px 0 15px 0;
        position: relative;
        text-align: center;
        @media @max-xs {
            padding: 5px 0;
        }
        .img{
            height: 80px;
            border-radius: 8px;
            @media @max-sm {
                height: 70px;
            }
            @media @max-xs {
                height: 45px;
            }
        }
        .zi{
            position:absolute;
            bottom:-15px;
            right: 20px;
            height: 80px;
            @media @max-sm {
                right: 0;
                height: 60px;
                bottom: 0;
            }
            @media @max-xs {
                bottom: auto;
                height: 30px;
                top: -15px;
            }
        }
    }
    .title{
        line-height: 40px;
        text-align: center;
        font-weight: bold;
        font-size: 18px;
        @media @max-xs {
            font-size: 14px;
            line-height: 20px;
            padding: 5px 10px 0 10px;
        }
    }
}
.bao_title{
    line-height: 100px;
    text-align:center;
    font-size: 36px;
    color: #1D2129;
    letter-spacing: 2px;
    font-weight:bold;
    @media @max-sm {
        font-size: 30px;
        line-height: 80px;
    }
    @media @max-xs {
        font-size: 21px;
        line-height: 30px;
        margin: 15px 0;
        letter-spacing: 0px;
        white-space: nowrap;
    }
}
.bao_two,.bao_three,.bao_four,.xiao_one,.xiao_two,.xiao_three{
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 50px;
    @media @max-sm {
        padding: 30px;
        border-radius: 15px;
    }
    @media @max-xs {
        flex-wrap: wrap;
        padding: 15px;
        margin-bottom: 30px;
        border-radius: 10px;
    }
    .pimg{
        width: 46%;
        border-radius: 10px;
        overflow: hidden;
        img{
            display: block;
            width: 100%;
            transition: 0.5s;
        }
        @media @max-xs {
            width: 100%;
            margin-top: 10px;
        }
    }
    .nei{
        width: 50%;
        @media @max-xs {
            width: 100%;
        }
    }
    /*
    &:hover{
        .pimg img{
            -webkit-transform:scale(1.1);
            -moz-transform:scale(1.1);
            transform:scale(1.1);
        }
    }*/
}
.bao_two{
    background: #E5EAF5;
    li{
        padding: 8px 0;
        display: flex;
        flex-wrap: wrap;
        @media @max-xs {
            padding: 5px 0;
        }        
    }
    h3{
        font-size: 20px;
        line-height: 40px;
        color: #1D2129;
        font-weight:bold;
        width: 100%;
        @media @max-xs {
            font-size: 16px;
            line-height: 40px;
        }    
    }
    span{
        padding: 0 15px;
        line-height: 40px;
        background: #FFFFFF;
        border-radius: 8px;
        margin-right: 14px;
        display: flex;
        align-items: center;
        font-size: 16px;
        img{
            width: 20px;
            margin-right: 8px;
            @media @max-xs {
                width: 16px;
                margin-right: 5px;
            }   
        }
        @media @max-sm {
            margin-bottom: 5px;
            margin-right: 10px;
            padding: 0 10px;
            font-size: 14px;
            line-height: 36px;
        }
        @media @max-xs {
            padding: 0 10px;
            white-space: nowrap;
        } 
        &:last-child{
            margin-right: 0;
        }  
    }
}
.bao_three{
    background: #FFF0E0;
    li{
        padding: 13px 0;
        line-height: 26px;
        display: flex;
        align-items: center;
        color: #1D2129;
        @media @max-xs {
            padding: 8px 0;
            line-height: 22px;
        }
    }
    .tit{
        padding-right: 32px;
        margin-right: 12px;
        background: url(../images/bg_28.png) no-repeat right center;
        background-size: 20px;
        font-size: 18px;
        font-weight: bold;
        white-space: nowrap; 
        @media @max-sm {
            font-size: 16px;
        }
        @media @max-xs {
            font-size: 15px;
            background-size: 18px;
            padding-right: 28px;
        }
    }
    .desc{
        font-size: 18px;
        @media @max-sm {
            font-size: 16px;
        }
        @media @max-xs {
            font-size: 15px;
        }
    }
}
.bao_four{
    background: #E5F1F5;
    li{
        display: inline-block;
        line-height: 60px;
        margin: 15px 0;
        padding: 0 30px 0 60px;
        border-radius: 50px;
        font-size: 17px;
        margin-right: 20px;
        background: url(../images/bg_29.png) no-repeat 20px center #fff;
        background-size: 24px;
        @media @max-sm {
            margin: 10px 0;
            line-height: 46px;
            padding: 0 20px 0 40px;
            background: url(../images/bg_29.png) no-repeat 14px center #fff;
            background-size:18px;
            font-size: 16px;
        }
        @media @max-xs {
            line-height: 42px;
            font-size: 15px;
        }
    }
}
.bao_five{
    overflow: hidden;
    border-radius: 20px;
    margin-bottom: 50px;
    img{
        display:block;
        width: 100%;
    }
    @media @max-sm {
        border-radius: 15px;
    }
    @media @max-xs {
        margin-bottom: 30px;
        border-radius: 10px;
    }
}
.anquan{
    background: #E1E6F1;
    padding: 30px 0 25px 0;
    border-radius: 20px;
    display: flex;
    margin-bottom: 80px;
    @media @max-sm {
        padding: 25px 0 20px 0;
        border-radius: 15px;
        margin-bottom: 50px;
    }
    @media @max-xs {
        border-radius: 10px;
        padding: 20px 0 15px 0;
        margin-bottom: 30px;
    }
    li{
        width: 33.333%;
        text-align: center;
        transition: 0.5s;
        // &:hover{
        //     margin-top: -15px;
        // }
    }
    .pimg{
        padding-bottom: 10px;
        img{
            height: 120px;
            @media @max-sm {
                height: 100px;
            }
            @media @max-xs {
                height: 60px;
            }
        }
    }
    .title{
        font-size: 20px;
        color: #1D2129;
        line-height: 38px;
        font-weight:bold;
        @media @max-sm {
            font-size: 18px;
        }
        @media @max-xs {
            font-size: 15px;
            line-height: 24px;
        }
    }
    .desc{
        font-size: 16px;
        color: #1D6AFF;
        line-height: 24px;
        @media @max-sm {
            font-size: 14px;
        }
        @media @max-xs {
            font-size: 12px;
            line-height: 20px;
        }
    }
}

.xiao_one,.xiao_two,.xiao_three{
    background: #E5F1F5;
    ul{
        @media @max-xs {
            margin-top: -15px;
        }
    }
    li{
        padding-left: 35px;
        background: url(../images/bg_29.png) no-repeat left 2px; 
        background-size: 24px;
        margin:28px 0;
        line-height: 28px;
        font-size: 19px;
        letter-spacing: 1px;
        @media @max-sm {
            padding-left: 30px;
            background: url(../images/bg_29.png) no-repeat left 2px; 
            background-size: 22px;
            line-height: 24px;
            font-size: 17px;
            margin:20px 0;
        }
        @media @max-xs {
            padding-left: 30px;
            background: url(../images/bg_29.png) no-repeat left 4px; 
            background-size: 20px;
            line-height: 24px;
            font-size: 15px;
            margin: 15px 0;
        }
    }
}
.xiao_two{
    background: #E5E5F5;
    li{
        background: url(../images/bg_29-2.png) no-repeat left 2px; 
        background-size: 24px;
        @media @max-sm {
            background: url(../images/bg_29-2.png) no-repeat left 2px; 
            background-size: 22px;
        }
        @media @max-xs {
            padding-left: 30px;
            background: url(../images/bg_29-2.png) no-repeat left 4px; 
            background-size: 20px;
        }
    }
}
.xiao_three{
    background: #FFF0E0;
    li{
        background: url(../images/bg_28.png) no-repeat left 4px; 
        background-size: 20px;
        @media @max-sm {
            background: url(../images/bg_28.png) no-repeat left 4px; 
            background-size: 20px;
        }
        @media @max-xs {
            padding-left: 30px;
            background: url(../images/bg_28.png) no-repeat left 4px; 
            background-size: 20px;
        }
    }
}

/* 会展 */
.hz_one{
    margin: 0 -10px;
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 40px;
    @media @max-sm {
        padding-bottom: 30px;
    }
    @media @max-xs {
        margin: 0;
        padding-bottom: 10px;
    }
    li{
        width: 33.333%;
        padding:10px;
        @media @max-xs {
            width: 100%;
            padding: 0 0 15px 0;
        }
    }
    .nei{
        background: #fff;
        border-radius: 12px;
        padding:40px 20px;
        display: block;
        width: 100%;
        height: 100%;
        text-align:center;
        transition: 0.5s;
        @media @max-sm {
            padding:30px 15px;
        }
        &:hover{
            margin-top: -15px;
            -moz-box-shadow:0px 0px 8px rgba(0,0,0,0.08);
            -webkit-box-shadow:0px 0px 8px rgba(0,0,0,0.08);
            box-shadow:0px 0px 8px rgba(0,0,0,0.08);
        }
    }
    .pimg{
        padding-bottom: 8px;
        img{
            height: 80px;
            border-radius: 8px;
        }
    }
    .title{
        line-height: 50px;
        font-weight: bold;
        color: #1D2129;
        font-size: 18px;
        letter-spacing: 1px;
    }
    .desc{
        font-size: 16px;
        color: #1D2129;
        line-height: 26px;
        @media @max-sm {
            font-size: 14px;
            line-height: 24px;
        }
    }
}
.hz_two{
    li{
        @media @max-xs {
            width: 33.33%;
        }
    }
}
.hz_list{
    li{
        width: 25%;
        @media @max-sm {
            width: 33.33%;
        }
        @media @max-xs {
            width: 50%;
        }
    }
}
.hz_more{
    line-height: 50px;
    text-align: center;
    a{
        color: #000;
        &:hover{
            color:@color1;
        }
    }
}

/* 展馆 */
.zg_yaosu{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    li{
        width: 49.1%;
        margin-bottom: 24px;
        padding: 25px;
        background: #fff;
        border-radius: 12px;
        transition: 0.5s;
        @media @max-xs {
            width: 100%;
            margin-bottom: 20px;
            padding: 15px;
            
            &:last-child{
                margin-bottom: 10px;
            }
        }
        .bottom{
            background: #FFEEE3;
            border-radius: 8px;
            font-size: 15px;
            color: #1D2129;
            text-align: center;
            line-height: 48px;
        }
        &:nth-child(2) .bottom{ background: #DEECFF;}
        &:nth-child(3) .bottom{ background: #FFF3D3;}
        &:nth-child(4) .bottom{ background: #D9FFDE;}
        &:hover{
            -moz-box-shadow:0px 0px 15px rgba(0,0,0,0.2);
            -webkit-box-shadow:0px 0px 15px rgba(0,0,0,0.2);
            box-shadow:0px 0px 15px rgba(0,0,0,0.2);
        }
    }
    .title{
        padding-bottom: 25px;
        line-height:26px;
        font-size: 19px;
        color: #1D2129;
        font-weight:bold;
        width: 100%;
        text-align: center;
        letter-spacing: 1px;
        @media @max-xs {
            font-size: 18px;
            padding-bottom: 15px;
        }
    }
    .info{
        display: flex;
        justify-content: space-between;
        margin: 0 -7px;
        padding-bottom: 20px;
        dl{
            width: 50%;
            margin: 0 7px;
            background: #F7F9FC;
            border-radius: 8px;
            padding: 15px 20px 20px 20px;
            text-align:center;
            @media @max-sm {
                padding: 10px 15px 15px 15px;
            }
            @media @max-xs {
                padding: 5px 10px 10px 10px;
            }
        }
        .tit{
            line-height: 24px;
            padding: 8px 0;
            color: #1D2129;
            font-size: 16px;
            font-weight: bold;
            @media @max-xs {
                font-size: 15px;
            }
        }
        .desc{
            height: 72px;
            color: #4E5969;
            line-height: 24px;
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            @media @max-xs {
                font-size: 13px;
                line-height: 23px;
                -webkit-line-clamp:7;
                height:auto;
                text-align: justify;
            }
        }
    }
}

/* 新会展crm  */
.crm1{
    text-align: center;
    padding-bottom: 50px;
    margin-top: -10px;
    @media @max-sm {
        padding-bottom: 40px;
    }
    @media @max-xs {
        padding-bottom: 30px;
    }
    .title{
        font-size: 24px;
        color: #1D2129;
        line-height: 40px;
        padding-bottom: 10px;
        font-weight: bold;
        @media @max-sm {
            font-size: 20px;
            line-height: 30px;
        }
        @media @max-xs {
            font-size: 16px;
            line-height: 24px;
        }
    }
    .desc{
        padding: 0 30px;
        font-size: 16px;
        color: #1D2129;
        line-height: 22px;
        @media @max-xs {
            padding: 0;
            font-size: 14px;
            text-align: justify; 
        }
    }
}
.crm{
    display: flex;
    justify-content: space-between;
    align-items:flex-start;
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 50px;
    background: #E5EAF5;
    @media @max-sm {
        padding: 30px;
        border-radius: 15px;
    }
    @media @max-xs {
        flex-wrap: wrap;
        padding: 15px;
        margin-bottom: 30px;
        border-radius: 10px;
    }
    .pimg{
        width: 46%;
        border-radius: 10px;
        overflow: hidden;
        img{
            display: block;
            width: 100%;
            transition: 0.5s;
        }
        @media @max-xs {
            width: 100%;
            margin-top: 10px;
        }
    }
    .nei{
        width: 50%;
        @media @max-xs {
            width: 100%;
        }
        .title{
            line-height: 30px;
            font-size: 22px;
            font-weight: bold;
            color: #1D2129;
            padding-bottom: 20px;
            @media @max-sm {
                font-size: 20px;
                padding-bottom: 15px;
            }
            @media @max-xs {
                font-size: 18px;
                padding-bottom: 14px;
            }
        }
        li{
            padding-left: 35px;
            background: url(../images/bg_29-3.png) no-repeat left 3px;
            color: #1D2129;
            font-size: 18px;
            line-height: 28px;
            padding-bottom: 20px;
            @media @max-sm {
                font-size: 16px;
                line-height: 24px;
                background-size: 18px;
                padding-left: 30px;
                padding-bottom: 15px;
            }
            @media @max-xs {
                font-size: 15px;
                background-size: 16px;
                padding-left: 26px;
            }
        }
        .info{
            padding-left: 35px;
            padding-top: 10px;
            @media @max-sm {
                padding-left: 30px;
            }
            @media @max-xs {
                padding-left: 26px;
                padding-bottom: 10px;
                padding-top: 0;
            }
            .num{
                display: block;
                width: 100%;
                font-size: 48px;
                color: #1D2129;
                line-height: 56px;
                font-weight: bold;
                @media @max-sm {
                    font-size: 38px;
                    line-height: 50px;
                }
                @media @max-xs {
                    font-size: 34px;
                    line-height: 40px;
                }
            }
            dd{
                color: #1D2129;
                font-size: 18px;
                line-height: 28px;
                @media @max-sm {
                    font-size: 16px;
                    line-height: 24px;
                }
                @media @max-xs {
                    font-size: 15px;
                }
            }
        }
        .img{
            display: block;
            max-width: 100%;
        }
    }
    // &:hover{
    //     .pimg img{
    //         -webkit-transform:scale(1.1);
    //         -moz-transform:scale(1.1);
    //         transform:scale(1.1);
    //     }
    // }
}
.crm2{
    background: #E5F1F5;
    .nei{
        li{
            background: url(../images/bg_29-4.png) no-repeat left 3px;
            @media @max-sm {
                background-size: 18px;
            }
            @media @max-xs {
                background-size: 16px;
            }
        }
    }
}
.crm3{
    background: #FFF0E0;
    flex-wrap: wrap;
    .nei{
        li{
            background: url(../images/bg_29-5.png) no-repeat left 3px;
            @media @max-sm {
                background-size: 18px;
            }
            @media @max-xs {
                background-size: 16px;
            }
        }
    }
    .list{
        display: flex;
        justify-content: space-between;
        width: 100%;
        @media @max-xs {
            flex-wrap: wrap;
        }
        li{
            width: 50%;
            background: rgba(255,255,255,0.5);
            border-radius:8px;
            padding: 20px;
            margin: 0 8px;
            margin-top: 35px;
            @media @max-sm {
                padding: 15px;
                width: 100%;
                margin-top: 15px;
            }
            &:last-child{
                margin-right: 0;
            }
            &:first-child{
                margin-left: 0;
            }
            @media @max-xs {
                margin: 0;
                margin-top: 15px;
            }
        }
        .tit{
            font-size: 16px;
            color: #1D2129;
            line-height: 24px;
            padding-bottom: 8px;
            font-weight:bold;
        }
        .desc{
            font-size: 13px;
            color: #4E5969;
            line-height: 22px;
            text-align: justify;
        }
    }
}
.crm4{
    background: #E5E5F5;
    .nei{
        li{
            background: url(../images/bg_29-2.png) no-repeat left 3px;
            background-size: 24px;
            @media @max-sm {
                background-size: 18px;
            }
            @media @max-xs {
                background-size: 16px;
            }
        }
    }
}
.crm5{
    flex-wrap: wrap;
    .top{
        width: 100%;
        display: block;
        text-align: center;
        padding-bottom: 40px;
        @media @max-sm {
            padding-bottom: 30px;
        }
        @media @max-xs {
            padding-bottom: 20px;
        }
        .tit{
            font-size: 26px;
            font-weight: bold;
            line-height: 40px;
            padding-bottom: 10px;
            @media @max-sm {
                font-size: 22px;
            }
            @media @max-xs {
                font-size: 18px;
                padding-bottom: 5px;
            }
        }
        .desc{
            font-size: 20px;
            color: #1D2129;
            line-height: 28px;
            @media @max-sm {
                font-size: 18px;
            }
            @media @max-xs {
                line-height: 24px;
                font-size: 15px;
            }
        }
    }
}
.crm6{
    background: #E0EAFF;
    .nei{
        display: flex;
        justify-content: space-between;
        padding-top: 30px;
        @media @max-sm {
            padding-top: 0px;
        }
        .info{
            width: 50%;
            @media @max-xs {
                
            }
        }
    }
}

/* 新数智现场 */
.mb0{ margin-bottom: 0 !important;}
.sz_title{
    text-align:center;
    padding: 70px 0 20px 0;
    @media @max-sm {
        padding: 50px 0 15px 0;
    }
    @media @max-xs {
        padding: 40px 0 15px 0;
    }
    h2{
        font-size: 36px;
        color: #1D2129;
        letter-spacing: 2px;
        font-weight:bold;
        line-height: 40px;
        @media @max-sm {
            font-size: 30px;
            line-height: 30px;
        }
        @media @max-xs {
            font-size: 21px;
            line-height: 20px;
        }
    }
    p{
        font-size: 18px;
        color: #1D2129;
        line-height: 30px;
        font-weight: normal;
        padding-top: 15px;
        @media @max-xs {
            font-size: 14px;
            line-height: 24px;
            padding-top: 9px;
        }
    }
}
.sz_one{
    text-align: center;
    font-weight:bold;
    font-size: 23px;
    color: #1D2129;
    line-height: 40px;
    letter-spacing: 1px;
    padding-bottom: 15px;
    @media @max-sm {
        font-size: 20px;
    }
    @media @max-xs {
        font-size: 16px;
        padding-bottom: 6px;
    }
}
.sz_two{
    display: flex;
    align-items: center;
    padding-bottom: 40px;
    @media @max-sm {
        padding-bottom: 30px;
    }
    @media @max-xs {
        padding-bottom: 20px;
    }
    li{
        width: 50%;
        background: #FFFFFF;
        box-shadow: 0px 20px 30px 0px rgba(0,0,0,0.05);
        border-radius: 12px;
        text-align: center;
        padding: 20px 0;
        @media @max-sm {
            padding: 15px 0;
        }
        @media @max-xs {
            border-radius: 8px;
            padding: 10px 0;
        }
        .img{
            height: 120px;
            border-radius: 8px;
            @media @max-sm {
                height: 90px;
            }
            @media @max-xs {
                height: 50px;
            }
        }
        h3{
            font-size: 18px;
            font-weight: bold;
            line-height: 32px;
            padding-top: 8px;
            @media @max-sm {
                font-size: 16px;
            }
            @media @max-xs {
                font-size: 14px;
                line-height: 20px;
                padding-top: 12px;
            }
        }
    }
    .jiantou{
        background:url(../images/bg_30.png) no-repeat center center;
        background-size: 24px;
        width: 54px;
        flex-shrink: 0;
        box-shadow:none;
        @media @max-sm {
            width: 42px;
            background-size: 20px;
        }
        @media @max-xs {
            width: 22px;
            background-size: 14px;
        }
    }
}
.sz_three{
    background: #FFFFFF;
    box-shadow: 0px 20px 30px 0px rgba(0,0,0,0.05);
    border-radius: 12px;
    display: flex;
    padding: 20px 0;
    margin-bottom: 40px;
    @media @max-xs {
        flex-wrap: wrap;
        padding-bottom: 0;
        margin-bottom: 20px;
    }
    li{
        width: 50%;
        text-align: center;
        .img{
            height: 40px;
            border-radius: 8px;
        }
        p{
            font-size: 14px;
            color: #1D2129;
            line-height: 22px;
            padding-top: 8px;
            @media @max-sm {
                font-size: 13px;
            }
            @media @max-xs {
                font-size: 12px;
                white-space: nowrap;
                
            }
        }
        @media @max-xs {
            width: 18%;
            margin-bottom: 15px;
        }
    }
    .jiantou{
        width: 24px;
        background: url(../images/bg_30.png) no-repeat center center;
        background-size: 24px;
        flex-shrink: 0;
        @media @max-sm {
            background-size: 20px;
        }
        @media @max-xs {
            background-size: 14px;
        }
    }
}
.sz_four{
    display: flex;
    margin: 0 -10px;
    flex-wrap: wrap;
    margin-top: -10px;
    @media @max-xs {
        margin: 0;
        justify-content: space-between;
        margin-bottom: -10px;
    }
    li{
        width: 33.333%;
        padding: 10px;
        &:hover{
            .pimg img{
                -webkit-transform:scale(1.1);
                -moz-transform:scale(1.1);
                transform:scale(1.1);
            }
            .nei{
                -moz-box-shadow:0px 0px 12px rgba(0,0,0,0.08);
                -webkit-box-shadow:0px 0px 12px rgba(0,0,0,0.08);
                box-shadow:0px 0px 12px rgba(0,0,0,0.08);
            }
        }
        @media @max-xs {
            padding: 0;
            width: 48.5%;
            margin-bottom: 15px;
        }
    }
    .nei{
        background: #fff;
        border-radius: 8px;
        padding: 12px;
        display: block;
        width: 100%;
        height: 100%;
        transition: 0.4s;
        @media @max-xs {
            padding: 8px;
        }
    }
    .pimg{
        border-radius: 8px;
        overflow: hidden;
        img{
            display: block;
            width: 100%;
        }
    }
    .title{
        padding-top: 5px;
        text-align: center;
        line-height: 50px;
        font-size: 22px;
        color: #1D2129;
        font-weight: bold;
        letter-spacing: 1px;
        @media @max-sm {
            line-height: 40px;
            font-size: 18px;
        }
        @media @max-xs {
            line-height: 40px;
            font-size: 16px;
        }
    }
    .desc{
        font-size: 15px;
        color: #4E5969;
        line-height: 24px;
        text-align: center;
        padding-bottom: 5px;
        @media @max-sm {
            font-size: 14px;
            line-height: 22px;
        }
        @media @max-xs {
            
        }
    }
}
.sz_five{
    background: #E1E6F1;
    border-radius: 20px;
    padding: 35px 95px;
    margin-top: 10px;
    @media @max-sm {
        padding: 35px 55px;
    }
    @media @max-xs {
        padding: 15px;
        margin-top: 5px;
    }
    .img{
        display: block;
        max-width: 100%;
    }
    .title{
        text-align: center;
        padding-bottom: 10px;
        font-weight: bold;
        font-size: 22px;
        color: #1D2129;
        line-height: 30px;
        @media @max-sm {
            font-size: 20px;
        }
        @media @max-xs {
            font-size: 16px;
            line-height: 26px;
        }
    }
    .bottom{
        padding-top: 30px;
        text-align: center;
        @media @max-xs {
            padding-bottom: 15px;
        }
        p{
            font-size: 22px;
            color: #1D2129;
            line-height: 30px;
            font-weight: bold;
            padding-bottom: 15px;
            @media @max-sm {
                font-size: 20px;
            }
            @media @max-xs {
                font-size: 16px;
                line-height: 26px;
                padding-bottom: 10px;
            }
        }
        .btn{
            display: inline-block;
            width: 200px;
            height: 48px;
            background: #1D6AFF;
            border-radius: 8px;
            color: #fff;
            line-height: 48px;
            font-size: 16px;
            &:hover{
                background: #0051E8;
            }
            @media @max-xs {
                width: 150px;
                height: 42px;
                line-height: 42px;
            }
        }
    }
}
.sz_six{
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-bottom: -20px;
    @media @max-sm {
        margin-bottom: -15px;
    }
    @media @max-xs {
        margin-bottom: -15px;
    }
    li{
        width: 49%;
        margin-bottom: 20px;
        background: #fff;
        border-radius: 20px;
        padding: 25px;
        display: flex;
        transition: 0.4s;
        @media @max-sm {
            padding: 15px;
            border-radius: 15px;
        }
        @media @max-xs {
            flex-wrap: wrap;
            width: 48.5%;
            margin-bottom: 15px;
            border-radius: 8px;
            padding: 8px;
            justify-content: flex-start;
        }
        &:hover{
            .pimg img{
                -webkit-transform:scale(1.1);
                -moz-transform:scale(1.1);
                transform:scale(1.1);
            }
            -moz-box-shadow:0px 0px 12px rgba(0,0,0,0.08);
            -webkit-box-shadow:0px 0px 12px rgba(0,0,0,0.08);
            box-shadow:0px 0px 12px rgba(0,0,0,0.08);
        }
    }
    .pimg{
        width: 240px;
        height: 160px;
        border-radius: 12px;
        overflow: hidden;
        flex-shrink: 0;
        img{
            display: block;
            width: 100%;
            height: 100%;
        }
        @media @max-sm {
            width: 160px;
            height: 100px;
        }
        @media @max-xs {
            width: 100%;
            height: auto;
            border-radius: 6px;
            img{
                height: auto;
            }
        }
    }
    .txt{
        width: 100%;
        padding-left: 25px;
        @media @max-sm {
            padding-left: 20px;
        }
        @media @max-xs {
            padding-left: 0;
            padding-top: 10px;
            height: 100%;
        }
        .title{
            padding-bottom: 15px;
            font-size: 20px;
            color: #1D2129;
            line-height: 24px;
            font-weight: bold;
            @media @max-sm {
                font-size: 18px;
                padding-bottom: 10px;
            }
            @media @max-xs {
                font-size: 16px;
            }
        }
        .desc{
            font-size: 16px;
            color: #4E5969;
            line-height: 24px;
            @media @max-sm {
                font-size: 14px;
                line-height: 22px;
            }
        }
    }
}
.sz_seven{
    padding: 12px;
    background: #fff;
    border-radius: 20px;
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;
    @media @max-sm {
        padding: 8px;
        border-radius: 15px;
    }
    @media @max-xs {
        padding: 5px;
        border-radius: 6px;
        margin-top: 0;
    }
    li{
        width: 33.33%;
        padding: 12px;
        @media @max-sm {
            padding: 8px;
        }
        @media @max-xs {
            padding: 5px;
        }
        &:hover{
            img{
                -webkit-transform:scale(1.05);
                -moz-transform:scale(1.05);
                transform:scale(1.05);
            }
        }
    }
    img{
        display: block;
        width: 100%;
        border-radius: 12px;
        @media @max-xs {
            border-radius: 5px;
        }
    }
}
.sz_eight{
    display: flex;
    flex-wrap: wrap;
    margin: 0 -12px;
    padding-bottom: 40px;
    @media @max-xs {
        margin: 0;
        justify-content: space-between;
        padding-bottom: 30px;
    }
    li{
        width: 25%;
        padding: 12px;
        @media @max-sm {
            width: 33.333%;
        }
        @media @max-xs {
            width: 48.1%;
            padding: 0;
            padding-bottom: 15px;
        }
    }
    .nei{
        background: #fff;
        display: block;
        width: 100%;
        padding: 12px;
        border-radius: 12px;
        @media @max-xs {
            border-radius: 8px;
        }
    }
    .pimg{
        width: 100%;
        display: block;
        height: 200px;
        border-radius: 8px;
        overflow: hidden;
        @media @max-sm {
            height: 160px;
        }
        @media @max-xs {
            height: 120px;
            border-radius: 6px;
        }
        img{
            width: 100%;
            height: 100%;
            display: block;
            object-fit: cover;
        }
    }
    h3{
        text-align: center;
        font-size: 15px;
        line-height: 26px;
        color: #1D2129;
        padding-top: 8px;
        font-weight: bold;
        overflow: hidden;text-overflow: ellipsis;
        display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;
        @media @max-xs {
            font-size: 14px;
            line-height: 22px;
            -webkit-line-clamp: 2;
        }
    }
}

.body_hidden{ overflow: hidden; height: 100vh;}

/* 参展商 */
.czs_one{
    padding: 80px 0 0 0;
    display: flex;
    flex-wrap: wrap;
    margin: 0 -12px;
    @media @max-sm {
        padding: 50px 0 0 0;
        margin: 0 -10px;
    }
    @media @max-xs {
        padding: 20px 0 0 0;
        margin: 0;
    }
    li{
        width: 33.333%;
        padding: 12px;
        @media @max-sm {
            padding: 10px;
        }
        @media @max-xs {
            padding:0px;
            width: 100%;
            margin-bottom: 15pxs;
        }
    }
    .nei{
        background: #fff;
        padding: 25px;
        border-radius: 12px;
        color: #1D2129;
        @media @max-sm {
            padding: 15px;
        }
        transition: 0.5s;
        &:hover{
            -moz-box-shadow:0px 0px 15px rgba(0,0,0,0.2);
            -webkit-box-shadow:0px 0px 15px rgba(0,0,0,0.2);
            box-shadow:0px 0px 15px rgba(0,0,0,0.2);
        }
    }
    .title{
        font-size: 18px;
        font-weight: bold;
        line-height: 28px;
        @media @max-sm {
            font-size: 17px;
        }
    }
    .desc{
        padding: 12px 0 24px 0;
        font-size: 16px;
        line-height: 26px;
        @media @max-sm {
            padding: 10px 0 15px 0;
            font-size: 15px;
            line-height: 24px;
        }
    }
    .img{
        display:block;
        width: 100%;
        border-radius: 8px;
    }
}
.czs_two{
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    padding-bottom: 20px;
    @media @max-xs {
        padding-bottom: 10px;
    }
    li{
        width: 49%;
        background: #fff;
        border-radius: 12px;
        margin-bottom: 24px;
        padding: 20px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        @media @max-xs {
            width: 100%;
            margin-bottom: 15px;
            padding: 15px;
        }
        
        transition: 0.5s;
        &:hover{
            -moz-box-shadow:0px 0px 15px rgba(0,0,0,0.2);
            -webkit-box-shadow:0px 0px 15px rgba(0,0,0,0.2);
            box-shadow:0px 0px 15px rgba(0,0,0,0.2);
        }
    }
    .img{
        flex-shrink: 0;
        width: 60px;
    }
    .nei{
        width: 100%;
        padding-left: 20px;
        @media @max-xs {
            padding-left: 15px;
        }
        .tit{
            line-height:30px;
            font-size: 18px;
            color: #1D2129;
            font-weight: bold;
            @media @max-xs {
                font-size: 17px;
            }
        }
        .desc{
            font-size: 16px;
            color: #1D2129;
            line-height: 24px;
            padding-top: 3px;
            @media @max-xs {
                font-size: 14px;
                line-height: 22px;
            }
        }
    }
}
.czs_three{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding-bottom: 50px;
    li{
        width: 49.1%;
        margin-bottom: 24px;
        padding: 25px;
        background: #fff;
        border-radius: 12px;
        transition: 0.5s;
        @media @max-xs {
            width: 100%;
            margin-bottom: 20px;
            padding: 15px;
            
            &:last-child{
                margin-bottom: 10px;
            }
        }
        &:hover{
            -moz-box-shadow:0px 0px 15px rgba(0,0,0,0.2);
            -webkit-box-shadow:0px 0px 15px rgba(0,0,0,0.2);
            box-shadow:0px 0px 15px rgba(0,0,0,0.2);
        }
    }
    .title{
        padding-bottom: 25px;
        line-height:26px;
        font-size: 22px;
        color: #1D2129;
        font-weight:bold;
        width: 100%;
        text-align: center;
        letter-spacing: 1px;
        @media @max-xs {
            font-size: 18px;
            padding-bottom: 15px;
        }
    }
    .img{
        display: block;
        width: 100%;
        border-radius: 10px;
    }
    .info{
        display: flex;
        justify-content: space-between;
        margin: 0 -7px;
        padding-bottom: 20px;
        dl{
            width: 50%;
            margin: 0 7px;
            background: #F7F9FC;
            border-radius: 8px;
            padding:15px 18px;
            @media @max-sm {
                padding: 10px 15px;
            }
            @media @max-xs {
                padding: 5px 10px 10px 10px;
            }
        }
        .tit{
            line-height: 24px;
            padding-bottom: 8px;
            color: #1D2129;
            font-size: 16px;
            font-weight: bold;
            @media @max-xs {
                font-size: 15px;
            }
        }
        .desc{
            color: #4E5969;
            line-height: 22px;
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp:8;
            -webkit-box-orient: vertical;
            @media @max-xs {
                font-size: 13px;
                line-height: 23px;
                -webkit-line-clamp:7;
                height:auto;
                text-align: justify;
            }
        }
    }
}

/* 动态展位图 */
.zwt1{
    background: #E5EAF5;
    .tit{
        color: #1D2129;
        font-size: 19px;
        font-weight: bold;
        @media @max-xs {
            font-size: 16px;
        }
    }
    .desc{
        padding: 6px 0;
        font-size: 16px;
        color: #1D2129;
        line-height: 26px;
        @media @max-xs {
            font-size: 14px;
            line-height: 22px;
        }
    }
    .tag{
        display: flex;
        dd{
            background: #fff;
            font-size: 14px;
            color: #1D2129;
            line-height: 22px;
            padding: 5px 10px;
            border-radius: 8px;
            margin-right: 10px;
            @media @max-xs {
                font-size: 12px;
                line-height: 18px;
            }
        }
    }
}
.zwt2{
    background: #FFF0E0;
    .nei li{
        background: url(../images/bg_29-5.png) no-repeat left 3px;
        background-size: 22px;
        @media @max-sm {
            background-size: 18px;
        }
        @media @max-xs {
            background-size: 16px;
        }
    }
}
.zwt3{
    background: #E5F1F5;
    .nei li{
        background: url(../images/bg_29-4.png) no-repeat left 3px;
        background-size: 22px;
        @media @max-sm {
            background-size: 18px;
        }
        @media @max-xs {
            background-size: 16px;
        }
    }
}
.zwt4{
    .info{
        margin: 0;
        flex-wrap: wrap;
        background: #F7F9FC;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 25px;
        padding-bottom: 0;
        @media @max-xs {
            margin-bottom: 15px;
        }
        dl{
            width: 100%;
            margin: 0;
            padding: 0;
            border-radius: 0;
            margin-bottom: 15px;
        }
    }
    .tag{
        display: flex;
        margin-bottom: 5px;
        margin-top: -5px;
        @media @max-xs {
            flex-wrap: wrap;
            margin-bottom: 10px;
        }
        span{
            padding-left: 20px;
            background: url(../images/booth/bg.png) no-repeat 5px center #D6DDEE;
            background-size: 10px;
            line-height: 24px;
            display: inline-block;
            margin-right: 10px;
            border-radius: 4px 4px 4px 4px;
            color: #000000;
            font-size: 12px;
            padding-right: 10px;
            margin-bottom: 10px;
            @media @max-xs {
                margin-right:5px;
                margin-bottom: 5px;
            }
        }
    }
}
.zwt5{
    background: #E1E6F1;
    position: relative;
    border-radius: 20px;
    margin: 170px 0 80px 0;
    padding: 40px;
    @media @max-sm {
        margin: 0px 0 70px 0;
        padding: 30px;
    }
    @media @max-xs {
        padding:20px 20px 25px 20px;
        border-radius: 10px;
        margin: -20px 0 40px 0;
    }
    .img{
        position: absolute;
        right: 0;
        bottom: 0;
        width: 50%;
    }
    h3{
        font-size: 24px;
        color: #1D2129;
        line-height: 36px;
        font-weight: bold;
        display: block;
        width: 100%;
        padding-bottom: 20px;
        padding-right: 40%;
        @media @max-sm {
            font-size: 20px;
        }
        @media @max-xs {
            padding-right: 15%;
            font-size: 18px;
            line-height: 32px;
            padding-bottom: 10px;
        }
    }
    .more{
        width: 200px;
        line-height: 48px;
        background: @color1;
        border-radius: 8px;
        display: inline-block;
        color: #fff;
        text-align: center;
        cursor: pointer;
        &:hover{
            background: @color3;
        }
        @media @max-sm {
            width: 170px;
            line-height: 44px;
        }
        @media @max-xs {
            width: 110px;
            line-height: 32px;
            font-size: 15px;
            border-radius: 5px;
        }
    }
}




@media @max-sm {
    
}
@media @max-xs {
    
}